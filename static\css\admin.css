/* =====================
   管理员专用样式（admin.css）
   ===================== */

/* ========== 1. 导航与布局 ========== */
/* 功能：左侧导航标签按钮的基础样式
   部位：管理员页面左侧导航栏中的四个主要功能标签按钮（年级管理、班级管理、教师管理、学生管理） */
#gradeTabBtn,
#classTabBtn,
#teacherTabBtn,
#studentTabBtn {
    font-weight: 600; /* 粗体字重 */
    font-size: 1.08em; /* 字体大小 */
    letter-spacing: 1px; /* 字母间距 */
    transition: all 0.2s; /* 过渡动画 */
}

/* 功能：激活状态的导航标签按钮样式
   部位：当前选中的导航标签按钮，显示为高亮状态 */
#gradeTabBtn.active,
#classTabBtn.active,
#teacherTabBtn.active,
#studentTabBtn.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0a58ca 100%) !important; /* 渐变背景 */
    color: #fff !important; /* 白色文字 */
    border: none; /* 无边框 */
    box-shadow: 0 4px 12px rgba(82,122,175,0.12); /* 阴影效果 */
}

/* 功能：主内容区域的容器样式
   部位：管理员页面右侧的主要内容显示区域，包含所有功能模块的内容 */
.main-content {
    background: #f8fafc; /* 浅灰色背景 */
    border-radius: 1.2rem; /* 圆角设计 */
    box-shadow: 0 6px 32px rgba(82,122,175,0.10); /* 阴影效果 */
    padding: 2.5rem 2.5rem 2rem 2.5rem; /* 内边距：上2.5rem 左右2.5rem 下2rem */
    min-height: 80vh; /* 最小高度为视口高度的80% */
}

/* 功能：页面分隔线样式
   部位：用于分隔不同内容区块的水平分隔线 */
hr.my-2 {
    border-top: 2px solid #e0e7ef; /* 顶部边框：2px实线，浅灰色 */
    margin: 0.5rem 0; /* 上下外边距0.5rem */
}

/* ========== 2. 成绩统计区块 ========== */
/* 功能：成绩统计信息的容器卡片
   部位：管理员页面中显示年级平均分统计和可视化图表的卡片容器 */
.grade-avg-section.card,
.grade-visualization.card {
    background: #fafdff; /* 浅蓝色背景 */
    border-radius: 1rem; /* 圆角设计 */
    box-shadow: 0 2px 12px #e0e7ef; /* 阴影效果 */
    margin-bottom: 2rem; /* 底部外边距 */
}

/* 功能：总平均分的主要显示区域
   部位：卡片中突出显示年级总平均分的核心区域 */
.grade-avg-main {
    background: #f8fafc; /* 浅灰色背景 */
    border-radius: 10px; /* 圆角设计 */
    box-shadow: 0 2px 8px #e0e7ef; /* 阴影效果 */
    padding: 20px 30px; /* 内边距 */
    text-align: center; /* 居中对齐 */
    min-width: 180px; /* 最小宽度 */
}

/* 功能：平均分标题文字
   部位：总平均分区域中显示"总平均分"等标题文字 */
.avg-title {
    font-size: 18px; /* 字体大小 */
    color: #333; /* 深灰色 */
    margin-bottom: 8px; /* 底部外边距 */
}

/* 功能：平均分数值显示
   部位：总平均分区域中显示具体分数数值，如"85.6" */
.avg-value {
    font-size: 36px; /* 大号字体 */
    color: #1890ff; /* 蓝色 */
    font-weight: bold; /* 粗体 */
    letter-spacing: 1px; /* 字母间距 */
}

/* 功能：各科目平均分的横向布局容器
   部位：总平均分下方横向排列各科目平均分的容器 */
.subject-avg-row {
    display: flex; /* 弹性布局 */
    flex-direction: row; /* 水平排列 */
    gap: 18px; /* 元素间距 */
    align-items: stretch; /* 垂直拉伸对齐 */
    justify-content: center; /* 水平居中对齐 */
    width: 100%; /* 占满容器宽度 */
    margin-top: 0.5rem; /* 顶部外边距 */
}

/* 功能：单个科目的平均分卡片
   部位：每个具体科目（如语文、数学、英语）的平均分显示卡片 */
.subject-avg-card {
    flex: 1 1 0; /* 弹性布局：可伸缩、可收缩、基础尺寸为0 */
    min-width: 110px; /* 最小宽度 */
    max-width: 150px; /* 最大宽度 */
    background: #fff; /* 白色背景 */
    border-radius: 12px; /* 圆角设计 */
    box-shadow: 0 2px 8px #e0e7ef; /* 阴影效果 */
    padding: 18px 0 12px 0; /* 内边距：上下18px 左右0 */
    text-align: center; /* 居中对齐 */
    font-size: 16px; /* 字体大小 */
    color: #444; /* 深灰色 */
    display: flex; /* 弹性布局 */
    flex-direction: column; /* 垂直排列 */
    align-items: center; /* 水平居中对齐 */
    transition: box-shadow 0.2s, transform 0.2s; /* 过渡动画 */
    margin-bottom: 0; /* 无底部外边距 */
}

/* 功能：科目平均分卡片悬停效果
   部位：当用户鼠标悬停在科目平均分卡片上时的视觉反馈 */
.subject-avg-card:hover {
    box-shadow: 0 4px 16px #b6c6e0; /* 悬停时阴影加深 */
    transform: translateY(-2px) scale(1.04); /* 悬停时向上移动2px并放大1.04倍 */
}

/* 功能：科目名称标签
   部位：科目平均分卡片中显示科目名称，如"语文"、"数学"等 */
.subject-avg-label {
    font-size: 15px; /* 字体大小 */
    color: #888; /* 浅灰色 */
    margin-bottom: 4px; /* 底部外边距 */
    letter-spacing: 1px; /* 字母间距 */
}

/* 功能：科目平均分数值
   部位：科目平均分卡片中显示具体分数 */
.subject-avg-value {
    font-size: 2rem; /* 大号字体 */
    color: #2b7cff; /* 蓝色 */
    font-weight: bold; /* 粗体 */
    margin-bottom: 2px; /* 底部外边距 */
    letter-spacing: 1px; /* 字母间距 */
}

/* 功能：分数单位显示
   部位：科目平均分卡片中显示"分"等单位的文字 */
.subject-avg-unit {
    font-size: 13px; /* 字体大小 */
    color: #aaa; /* 浅灰色 */
}

/* 功能：可视化图表的整体容器
   部位：成绩统计卡片中展示各种图表（柱状图、雷达图、表格）的容器 */
.grade-visualization {
    display: flex; /* 弹性布局 */
    flex-direction: row; /* 水平排列 */
    gap: 32px; /* 元素间距 */
    margin-top: 10px; /* 顶部外边距 */
    justify-content: flex-start; /* 左对齐 */
    align-items: flex-start; /* 顶部对齐 */
    flex-wrap: wrap; /* 允许换行 */
}

/* 功能：单个图表的包装容器
   部位：每个具体图表的包装器，包含图片和操作按钮 */
.visual-img-wrapper {
    position: relative; /* 相对定位 */
    display: flex; /* 弹性布局 */
    flex-direction: column; /* 垂直排列 */
    align-items: center; /* 水平居中对齐 */
    margin-bottom: 8px; /* 底部外边距 */
    width: 100%; /* 占满容器宽度 */
    height: 100%; /* 占满容器高度 */
    overflow: hidden; /* 隐藏溢出内容 */
}

/* 功能：图表图片显示
   部位：包装器中显示的具体图表图片，如柱状图、雷达图等 */
.visual-img {
    width: 100%; /* 占满容器宽度 */
    height: 280px; /* 固定高度 */
    object-fit: contain; /* 保持图片比例 */
    border-radius: 0.5rem; /* 圆角设计 */
    max-width: 100%; /* 最大宽度100% */
    max-height: 100%; /* 最大高度100% */
    box-sizing: border-box; /* 盒模型 */
}

/* 功能：图表操作按钮区域
   部位：图表下方显示操作按钮（如查看、下载）的区域 */
.visual-img-action {
    display: flex; /* 弹性布局 */
    gap: 10px; /* 按钮间距 */
    margin-top: 8px; /* 顶部外边距 */
    justify-content: center; /* 居中对齐 */
    width: 100%; /* 占满容器宽度 */
}

/* 功能：图表操作按钮样式
   部位：图表操作区域中的具体按钮样式 */
.visual-img-action .btn {
    padding: 0.25em 0.9em; /* 内边距 */
    font-size: 1em; /* 字体大小 */
    border-radius: 0.5em; /* 圆角设计 */
    box-shadow: 0 1px 4px #e0e7ef; /* 阴影效果 */
}

/* ========== 3. 管理员页面特定组件样式 ========== */
/* 继承public.css中的基础组件样式，这里只定义管理员页面特有的样式 */

/* 功能：管理员页面中所有卡片的基础样式
   部位：管理员页面中各种信息展示卡片，如成绩统计卡片、班级信息卡片等 */
.card {
    border-radius: 1.1rem !important; /* 圆角设计 */
    box-shadow: 0 4px 24px rgba(82,122,175,0.10) !important; /* 阴影效果 */
    margin-bottom: 2rem; /* 底部外边距 */
}

/* 功能：卡片悬停时的交互效果
   部位：当用户鼠标悬停在卡片上时的视觉反馈 */
.card:hover {
    box-shadow: 0 8px 32px rgba(82,122,175,0.16) !important; /* 悬停时阴影加深 */
}

/* 功能：卡片标题中的图标样式
   部位：卡片头部标题左侧显示的图标，如统计图标、管理图标等 */
.card .card-header .card-title i {
    font-size: 1.2em; /* 图标字体大小 */
    margin-right: 0.5em; /* 右侧外边距 */
}

/* 功能：管理员页面中所有按钮的基础样式
   部位：管理员页面中的各种操作按钮，如添加、编辑、删除按钮等 */
.btn {
    border-radius: 0.6rem; /* 按钮圆角 */
    font-size: 1.08em; /* 按钮字体大小 */
}

/* 功能：管理员页面中所有徽章的基础样式
   部位：管理员页面中显示状态、标签等信息的徽章，如角色徽章、状态徽章等 */
.badge {
    border-radius: 0.5em; /* 徽章圆角 */
    font-size: 0.98em; /* 徽章字体大小 */
    padding: 0.4em 0.8em; /* 徽章内边距 */
    background: #e9f2ff; /* 浅蓝色背景 */
    color: #2b7cff; /* 蓝色文字 */
}

/* 功能：管理员页面中所有表格的基础样式
   部位：管理员页面中显示数据的表格，如班级列表、教师列表、学生列表等 */
.admin-table {
    border-radius: 0.7rem; /* 表格圆角 */
    font-size: 1.08em; /* 表格字体大小 */
}

/* 功能：表格表头样式
   部位：表格顶部的标题行，显示列名 */
.admin-table th {
    font-size: 1.08em; /* 表头字体大小 */
}

/* 功能：表格数据单元格样式
   部位：表格中显示具体数据的单元格 */
.admin-table td {
    font-size: 1.05em; /* 单元格字体大小 */
    padding: 0.7em 0.5em; /* 单元格内边距 */
}

/* 功能：表格行悬停效果
   部位：当用户鼠标悬停在表格行上时的视觉反馈 */
.admin-table tbody tr:hover {
    background: #f0f6ff; /* 悬停时浅蓝色背景 */
}

/* ========== 4. 图表组件 ========== */
/* 功能：平均分图表的容器卡片
   部位：管理员页面中显示年级平均分趋势图的主要容器 */
.grade-avg-chart-card {
    width: 100%; /* 占满容器宽度 */
    max-width: 100%; /* 最大宽度100% */
    margin: 0 auto; /* 水平居中对齐 */
    background: #fafdff; /* 浅蓝色背景 */
    border-radius: 1.2rem; /* 圆角设计 */
    box-shadow: 0 6px 32px #e0e7ef; /* 阴影效果 */
    padding: 2.2rem 2.2rem 1.5rem 2.2rem; /* 内边距 */
    margin-bottom: 2.5rem; /* 底部外边距 */
    border: none; /* 无边框 */
    display: flex; /* 弹性布局 */
    flex-direction: column; /* 垂直排列 */
    align-items: center; /* 水平居中对齐 */
}

/* 功能：平均分图表的标题
   部位：图表卡片顶部显示的标题，如"年级平均分趋势图" */
#gradeAvgChartTitle {
    font-size: 1.35rem; /* 标题字体大小 */
    color: var(--primary-color); /* 主题色 */
    font-weight: 700; /* 粗体字重 */
    margin-bottom: 1.2rem; /* 底部外边距 */
    margin-left: 0.5rem; /* 左侧外边距 */
    letter-spacing: 1px; /* 字母间距 */
    align-self: flex-start; /* 左对齐 */
}

/* 功能：平均分图表的容器
   部位：图表卡片中实际显示图表的区域 */
#gradeAvgChart {
    width: 100%; /* 占满容器宽度 */
    max-width: 100%; /* 最大宽度100% */
    min-height: 260px; /* 最小高度 */
    margin: 0 auto; /* 水平居中对齐 */
    padding: 1.2rem 0 0.5rem 0; /* 内边距：上下1.2rem 左右0 */
    background: transparent; /* 透明背景 */
    border-radius: 1.1rem; /* 圆角设计 */
    box-shadow: none; /* 无阴影 */
    display: flex; /* 弹性布局 */
    justify-content: center; /* 水平居中对齐 */
    align-items: center; /* 垂直居中对齐 */
}

/* 功能：平均分图表的画布容器
   部位：图表容器中实际绘制图表的画布区域 */
#gradeAvgChartCanvas {
    background: #fff; /* 白色背景 */
    border-radius: 1.1rem; /* 圆角设计 */
    box-shadow: 0 2px 12px #e0e7ef; /* 阴影效果 */
    padding: 1.2rem; /* 内边距 */
    width: 100%; /* 占满容器宽度 */
    max-width: 100%; /* 最大宽度100% */
    margin: 0 auto; /* 水平居中对齐 */
    display: block; /* 块级显示 */
}

/* 功能：成绩信息的弹性布局容器
   部位：成绩统计页面中左侧图表和右侧信息的布局容器 */
.grade-info-flex {
    display: flex; /* 弹性布局 */
    gap: 2.5rem; /* 元素间距 */
    flex-wrap: wrap; /* 允许换行 */
    align-items: stretch; /* 垂直拉伸对齐 */
    margin-bottom: 2.2rem; /* 底部外边距 */
}

/* 功能：成绩信息左侧区域
   部位：弹性布局中左侧显示详细成绩信息的区域 */
.grade-info-left {
    flex: 1 1 320px; /* 弹性布局：可伸缩、可收缩、基础尺寸320px */
    min-width: 260px; /* 最小宽度 */
    display: flex; /* 弹性布局 */
    flex-direction: column; /* 垂直排列 */
    gap: 1.5rem; /* 元素间距 */
}

/* 功能：成绩信息右侧区域
   部位：弹性布局中右侧显示补充信息的区域 */
.grade-info-right {
    flex: 1 1 260px; /* 弹性布局：可伸缩、可收缩、基础尺寸260px */
    min-width: 220px; /* 最小宽度 */
    display: flex; /* 弹性布局 */
    align-items: center; /* 垂直居中对齐 */
    justify-content: center; /* 水平居中对齐 */
}

/* 功能：右侧区域中的信息卡片
   部位：右侧区域中显示具体信息（如统计摘要）的卡片 */
.grade-info-right .p-2.border.rounded.bg-light {
    min-width: 200px; /* 最小宽度 */
    max-width: 260px; /* 最大宽度 */
    margin: 0 auto; /* 水平居中对齐 */
    padding: 1.2rem 0.5rem !important; /* 内边距 */
}



/* ========== 5. 图片查看弹窗 ========== */
/* 图片弹窗容器样式 */
.swal2-image-popup {
    border-radius: 1.5rem !important; /* 圆角设计 */
    box-shadow: 0 8px 48px rgba(40,60,120,0.18); /* 阴影效果 */
    background: linear-gradient(135deg, #f8fafc 60%, #e3eafc 100%) !important; /* 渐变背景 */
    padding: 2.5rem 2rem 2rem 2rem !important; /* 内边距 */
    max-width: 1100px !important; /* 最大宽度 */
    min-width: 340px; /* 最小宽度 */
    min-height: 320px; /* 最小高度 */
}

/* 弹窗中的图片样式 */
.swal2-image {
    border-radius: 1rem !important; /* 图片圆角 */
    box-shadow: 0 4px 32px #b6c6e0; /* 图片阴影 */
    background: #fff; /* 白色背景 */
    margin-bottom: 1.2rem; /* 底部间距 */
    max-height: 72vh !important; /* 最大高度为视口72% */
    object-fit: contain !important; /* 保持图片比例 */
    padding: 0.5rem; /* 内边距 */
}

/* 弹窗标题样式 */
.swal2-image-popup .swal2-title {
    font-size: 2.1rem !important; /* 标题字体大小 */
    font-weight: 900 !important; /* 粗体字重 */
    color: #1a237e !important; /* 深蓝色 */
    text-align: center !important; /* 居中对齐 */
    margin-bottom: 1.2rem !important; /* 底部间距 */
    letter-spacing: 1.5px; /* 字母间距 */
}

/* 弹窗中的拒绝按钮样式（通常用作关闭按钮） */
.swal2-image-popup .swal2-deny {
    font-size: 1.15rem !important; /* 按钮字体大小 */
    font-weight: 700 !important; /* 粗体字重 */
    padding: 0.6rem 2.2rem; /* 按钮内边距 */
    background: linear-gradient(135deg, #4f8cff 0%, var(--primary-color) 100%) !important; /* 渐变背景 */
    box-shadow: 0 2px 12px #b6c6e0; /* 按钮阴影 */
    border-radius: 0.7rem !important; /* 按钮圆角 */
    margin-top: 1.2rem; /* 顶部间距 */
    transition: background 0.2s, box-shadow 0.2s; /* 过渡动画 */
}

/* 拒绝按钮悬停效果 */
.swal2-image-popup .swal2-deny:hover {
    background: linear-gradient(135deg, var(--primary-color) 0%, #4f8cff 100%) !important; /* 悬停时渐变反转 */
    box-shadow: 0 6px 24px #b6c6e0; /* 悬停时阴影加深 */
}

/* 弹窗关闭按钮样式 */
.swal2-image-popup .swal2-close {
    font-size: 2.2rem !important; /* 关闭按钮字体大小 */
    color: #4f8cff !important; /* 蓝色 */
    top: 18px !important; /* 距离顶部位置 */
    right: 18px !important; /* 距离右侧位置 */
    opacity: 0.85; /* 初始透明度 */
    transition: color 0.2s; /* 颜色过渡动画 */
}

/* 关闭按钮悬停效果 */
.swal2-image-popup .swal2-close:hover {
    color: #0a58ca !important; /* 悬停时颜色变深 */
    opacity: 1; /* 悬停时完全不透明 */
}

/* 功能：显示成绩分析图表的容器盒子
   部位：管理员页面中展示柱状图、雷达图、表格等可视化图表的单个容器 */
.visual-img-box {
    flex: 1 1 0; /* 弹性布局：可伸缩、可收缩、基础尺寸为0 */
    min-width: 260px; /* 最小宽度确保内容可读性 */
    max-width: 340px; /* 最大宽度限制，避免过宽 */
    margin: 0 8px 16px 0; /* 外边距：上0 右8px 下16px 左0 */
    background: #fff; /* 白色背景 */
    border-radius: 1rem; /* 圆角设计 */
    box-shadow: 0 2px 12px #e0e7ef; /* 阴影效果增强立体感 */
    display: flex; /* 弹性布局 */
    flex-direction: column; /* 垂直排列 */
    align-items: center; /* 水平居中对齐 */
    padding: 18px 12px 12px 12px; /* 内边距：上18px 左右12px 下12px */
    transition: box-shadow 0.2s, transform 0.2s; /* 过渡动画：阴影和变换 */
    overflow: hidden; /* 隐藏溢出内容 */
    box-sizing: border-box; /* 盒模型：包含边框和内边距 */
    height: auto; /* 自动高度 */
    min-height: 320px; /* 最小高度确保视觉平衡 */
}

/* 功能：鼠标悬停时的交互效果
   部位：当用户鼠标悬停在图表盒子上时的视觉反馈 */
.visual-img-box:hover {
    box-shadow: 0 8px 32px #b6c6e0; /* 悬停时阴影加深 */
    transform: translateY(-2px) scale(1.04); /* 悬停时向上移动2px并放大1.04倍 */
}

/* 功能：图表卡片的行容器，用于组织多个图表卡片
   部位：管理员页面中横向排列多个图表卡片的容器 */
.grade-visualization-row {
    display: flex; /* 弹性布局 */
    flex-direction: row; /* 水平排列 */
    justify-content: center; /* 水平居中对齐 */
    align-items: stretch; /* 垂直拉伸对齐 */
    gap: 32px; /* 元素间距 */
    margin-bottom: 2.2rem; /* 底部外边距 */
    flex-wrap: wrap; /* 允许换行 */
    width: 100%; /* 占满容器宽度 */
    min-height: 300px; /* 最小高度 */
}

/* 功能：单个图表卡片容器，包含图表标题、图片和操作按钮
   部位：每个具体的图表展示卡片，如柱状图卡片、雷达图卡片等 */
.grade-visualization-row .card {
    border-radius: 1.3rem; /* 卡片圆角 */
    box-shadow: 0 4px 24px #b6c6e0; /* 卡片阴影 */
    background: #fafdff; /* 浅蓝色背景 */
    transition: box-shadow 0.25s, transform 0.22s; /* 过渡动画 */
    border: none; /* 无边框 */
    min-height: 340px; /* 最小高度 */
    margin-bottom: 1.2rem; /* 底部外边距 */
    height: 100%; /* 占满父容器高度 */
    overflow: hidden; /* 隐藏溢出内容 */
}

/* 功能：卡片悬停时的交互效果
   部位：当用户鼠标悬停在图表卡片上时的视觉反馈 */
.grade-visualization-row .card:hover {
    box-shadow: 0 12px 48px #a0b8e0; /* 悬停时阴影加深 */
    transform: translateY(-4px) scale(1.035); /* 悬停时向上移动4px并放大1.035倍 */
}

/* 功能：图表卡片的标题区域
   部位：卡片顶部显示图表类型名称的区域，如"柱状图"、"雷达图"等 */
.grade-visualization-row .card-header {
    border-radius: 1.3rem 1.3rem 0 0; /* 顶部圆角 */
    background: #e3eafc; /* 浅蓝色背景 */
    font-size: 1.25rem; /* 字体大小 */
    font-weight: 800; /* 粗体字重 */
    color: #2b7cff; /* 蓝色文字 */
    border-bottom: none; /* 无底部边框 */
    padding: 1.1rem 1.2rem 0.7rem 1.2rem; /* 内边距 */
}

/* 功能：图表卡片的标题文字
   部位：卡片头部中显示的具体标题，如"成绩柱状图"、"能力雷达图"等 */
.grade-visualization-row .card-title {
    font-size: 1.15rem; /* 标题字体大小 */
    font-weight: 700; /* 粗体字重 */
    color: #1a237e; /* 深蓝色 */
    letter-spacing: 1px; /* 字母间距 */
    margin-bottom: 0; /* 无底部外边距 */
}

/* 功能：图表卡片的主体内容区域
   部位：卡片中间显示图表图片的区域 */
.grade-visualization-row .card-body {
    padding: 1.2rem 1.2rem 0.7rem 1.2rem; /* 内边距 */
    display: flex; /* 弹性布局 */
    align-items: center; /* 垂直居中对齐 */
    justify-content: center; /* 水平居中对齐 */
    min-height: 180px; /* 最小高度 */
}

/* 功能：图表卡片中显示的具体图表图片
   部位：卡片主体中展示的柱状图、雷达图、表格等图片 */
.grade-visualization-row .chart-image {
    border-radius: 0.7rem; /* 图片圆角 */
    box-shadow: 0 2px 12px #e0e7ef; /* 图片阴影 */
    background: #fff; /* 白色背景 */
    padding: 0.4rem; /* 内边距 */
    max-width: 100%; /* 最大宽度100% */
    max-height: 180px; /* 最大高度 */
    object-fit: contain; /* 保持图片比例 */
    transition: box-shadow 0.2s, transform 0.2s; /* 过渡动画 */
    cursor: pointer; /* 鼠标指针样式 */
    width: 100%; /* 宽度100% */
    height: 280px; /* 固定高度 */
}

/* 功能：图表图片悬停时的交互效果
   部位：当用户鼠标悬停在图表图片上时的视觉反馈 */
.grade-visualization-row .chart-image:hover {
    box-shadow: 0 8px 32px #b6c6e0; /* 悬停时阴影加深 */
    transform: scale(1.04); /* 悬停时放大1.04倍 */
}

/* 功能：图表卡片的底部操作区域
   部位：卡片底部显示操作按钮的区域，如"查看大图"、"下载"等按钮 */
.grade-visualization-row .card-footer {
    background: #f4f8ff; /* 浅蓝色背景 */
    border-radius: 0 0 1.3rem 1.3rem; /* 底部圆角 */
    border-top: none; /* 无顶部边框 */
    padding: 0.8rem 1.2rem; /* 内边距 */
    text-align: center; /* 文字居中对齐 */
}

/* 功能：图表卡片中的主要操作按钮
   部位：卡片底部的主要按钮，通常用于查看大图或执行主要操作 */
.grade-visualization-row .btn-outline-primary {
    font-size: 1.1rem; /* 按钮字体大小 */
    font-weight: 600; /* 粗体字重 */
    border-radius: 0.6rem; /* 按钮圆角 */
    padding: 0.5rem 1.5rem; /* 按钮内边距 */
    transition: background 0.2s, color 0.2s, box-shadow 0.2s; /* 过渡动画 */
    box-shadow: 0 2px 8px #e0e7ef; /* 按钮阴影 */
}

/* 功能：主要操作按钮悬停时的交互效果
   部位：当用户鼠标悬停在主要操作按钮上时的视觉反馈 */
.grade-visualization-row .btn-outline-primary:hover {
    background: linear-gradient(135deg, #4f8cff 0%, var(--primary-color) 100%); /* 悬停时渐变背景 */
    color: #fff; /* 悬停时白色文字 */
    box-shadow: 0 6px 24px #b6c6e0; /* 悬停时阴影加深 */
}

/* 功能：响应式布局的列容器
   部位：在不同屏幕尺寸下控制图表卡片排列的列容器 */
.grade-visualization-row .col-md-6,
.grade-visualization-row .col-lg-4 {
    display: flex; /* 弹性布局 */
    flex-direction: column; /* 垂直排列 */
}

/* ========== 6. 班级管理 ========== */
/* 班级卡片 */
.class-card {
    background: #fff;
    border-radius: 1.1rem;
    box-shadow: 0 2px 12px #e0e7ef;
    padding: 1.3rem 1.7rem 1.1rem 1.7rem;
    cursor: pointer;
    transition: box-shadow 0.2s, transform 0.2s, border 0.2s;
    border: 2px solid transparent;
    position: relative;
    min-height: 110px;
    margin-bottom: 0.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.class-card.selected,
.class-card:hover {
    box-shadow: 0 8px 32px #b6c6e0;
    border: 2px solid var(--primary-color);
    transform: translateY(-2px) scale(1.03);
}
.class-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.2em;
}
.class-card-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2b7cff;
}
.class-card-actions {
    display: flex;
    gap: 0.5em;
}
.class-card-actions .btn {
    padding: 0.2em 0.8em;
    font-size: 1em;
    border-radius: 0.5em;
}
.class-card-meta {
    display: flex;
    gap: 0.7em;
    flex-wrap: wrap;
    margin-bottom: 0.2em;
}
.class-card-meta .badge {
    font-size: 1em;
    padding: 0.35em 1em;
}
.class-card-teachers {
    color: #1a237e;
    font-size: 1.12em;
    font-weight: bold;
    margin-top: 0.2em;
    word-break: break-all;
    background: #e3f2fd;
    border-radius: 0.5em;
    padding: 0.3em 0.7em;
    display: flex;
    align-items: center;
    gap: 0.5em;
}
.subject-teacher-badge {
    background: #bbdefb;
    color: #1565c0;
    border-radius: 0.5em;
    padding: 0.2em 0.7em;
    margin-right: 0.3em;
    font-weight: 600;
    display: inline-block;
}
.subject-teacher-name {
    color: #0d47a1;
    font-weight: bold;
    margin-left: 0.3em;
}
.class-card-empty {
    color: #bbb;
    font-size: 1.1em;
    margin: 1.5em 0;
    text-align: center;
}
#addGradeBtn,
#importGradeBtn,
#addExamBtn,
#importExamBtn,
#addClassBtn,
#importClassBtn {
    font-size: 1.08rem;
    border-radius: 12px;
    font-weight: 600;
    height: 40px;
    padding: 0 22px;
}
#classSearchInput {
    border-radius: 12px;
    font-size: 1.08rem;
    padding: 0 18px;
    background: #fff;
    border: 1.5px solid #d1d5db;
    box-shadow: none;
    height: 40px;
    transition: border-color 0.2s, box-shadow 0.2s;
}
#classSearchInput:focus {
    border-color: #2563eb;
    box-shadow: 0 0 0 2px #2563eb22;
    outline: none;
}

/* 班级筛选器样式 */
#classTypeFilter {
    height: 40px;
    border-radius: 12px;
    border: 1.5px solid #d1d5db;
    font-size: 1.08rem;
    color: #22223a;
    background: #fff;
    transition: border-color 0.2s, box-shadow 0.2s;
    box-shadow: none;
    padding: 0 18px;
    margin-right: 8px;
}

#classTypeFilter:focus {
    border-color: #2563eb;
    box-shadow: 0 0 0 2px #2563eb22;
    outline: none;
}

#classTypeFilter option {
    font-size: 1.05rem;
    color: #374151;
    padding: 6px 12px;
}

#classTypeFilter:disabled {
    background: #f3f4f6;
    color: #b0b3bb;
    border-color: #e5e7eb;
}

.class-detail-modal {
  padding: 0.5em 0.5em 0.5em 0.5em;
}
.class-detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.2em;
}
.class-detail-title {
  font-size: 2rem;
  color: #2b7cff;
  font-weight: 900;
  letter-spacing: 1.2px;
}
.class-edit-btn {
  font-size: 1em;
  border-radius: 0.5em;
  padding: 0.2em 1.1em;
}
.class-detail-info-row {
  display: flex;
  gap: 2.2em;
  flex-wrap: wrap;
  margin-bottom: 1.2em;
  font-size: 1.12em;
}
.class-detail-label {
  color: #527aaf;
  font-weight: 700;
}
.class-detail-value {
  color: #222;
  font-weight: 700;
  margin-left: 0.2em;
}
.class-detail-section {
  margin-bottom: 1.2em;
}
.subject-teacher-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5em 1.2em;
  margin-top: 0.2em;
}
.subject-teacher-item.subject-teacher-badge {
    background: #bbdefb;
    color: #1565c0;
    border-radius: 0.5em;
    padding: 0.3em 0.9em;
    font-size: 1em;
    margin-bottom: 0.2em;
    font-weight: 600;
    display: inline-block;
}
.class-detail-avg-title {
  font-size: 1.25em;
  color: #2b7cff;
  font-weight: 800;
  margin-bottom: 0.5em;
}
.class-detail-avg-value {
  color: #1890ff;
  font-size: 1.5em;
  font-weight: bold;
  margin-left: 0.5em;
}
.class-detail-subject-avg {
  display: flex;
  gap: 1.2em;
  flex-wrap: nowrap;
  margin-bottom: 0.7em;
  overflow-x: auto;
  padding-bottom: 0.2em;
}
.class-detail-subject-avg-item {
  background: #fff;
  border-radius: 0.7em;
  box-shadow: 0 2px 8px #e0e7ef;
  padding: 0.7em 1.2em;
  font-size: 1em;
  color: #444;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 100px;
  max-width: 120px;
  flex: 0 0 auto;
}
.class-detail-subject-avg-label {
  color: #888;
  font-size: 0.98em;
  margin-bottom: 0.2em;
}
.class-detail-subject-avg-value {
  color: #2b7cff;
  font-size: 1.15em;
  font-weight: bold;
}

.edit-class-modal {
  background: #fff;
  border-radius: 1em;
  box-shadow: 0 4px 24px rgba(0,0,0,0.12);
  padding: 2em 2em 1em 2em;
  max-width: 600px;
  margin: 0 auto;
}
.edit-section {
  margin-bottom: 1.5em;
}
.section-title {
  font-size: 1.15em;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 0.7em;
  border-left: 4px solid #1976d2;
  padding-left: 0.5em;
}
.required {
  color: #e53935;
  font-weight: bold;
}
#subjectTeacherList .subject-row {
  display: flex;
  align-items: center;
  gap: 1em;
  margin-bottom: 0.5em;
  background: #f5faff;
  border-radius: 0.5em;
  padding: 0.5em 0.7em;
  box-shadow: 0 1px 4px rgba(25,118,210,0.06);
}
#subjectTeacherList .subject-row select,
#subjectTeacherList .subject-row input {
  min-width: 120px;
  max-width: 180px;
}
#subjectTeacherList .remove-subject-btn {
  color: #e53935;
  background: none;
  border: none;
  font-size: 1.2em;
  cursor: pointer;
  margin-left: 0.5em;
  transition: color 0.2s;
}
#subjectTeacherList .remove-subject-btn:hover {
  color: #b71c1c;
}
#addSubjectBtn {
  border-radius: 1em;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(25,118,210,0.08);
  transition: background 0.2s;
}
#addSubjectBtn:hover {
  background: #e3f2fd;
}
.edit-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1em;
}
.edit-modal-footer .btn {
  min-width: 90px;
  border-radius: 0.7em;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(25,118,210,0.08);
}

#editClassModal .modal-dialog {
    max-width: 750px !important;
}
#editClassModal .modal-content {
    border-radius: 22px !important;
    background: #fafdff !important;
    box-shadow: 0 8px 40px 0 rgba(60,60,90,0.13) !important;
    border: none !important;
}
#editClassModal .modal-header {
    background: linear-gradient(90deg, #2563eb 0%, #4e73df 100%) !important;
    color: white !important;
    border-radius: 22px 22px 0 0 !important;
    padding: 28px 32px 24px 32px !important;
    border-bottom: none !important;
}
#editClassModal .modal-title {
    font-size: 1.8rem !important;
    font-weight: 800 !important;
    color: white !important;
}
#editClassModal .btn-close {
    filter: brightness(0) invert(1) !important;
    opacity: 0.8 !important;
}
#editClassModal .btn-close:hover {
    opacity: 1 !important;
}
#editClassModal .modal-body {
    padding: 32px 40px 24px 40px !important;
}
#editClassModal .form-label {
    font-weight: 700 !important;
    color: #22304a !important;
    font-size: 1.08rem !important;
    margin-bottom: 8px !important;
}
#editClassModal .form-control, #editClassModal .form-select {
    border-radius: 13px !important;
    height: 48px !important;
    font-size: 1.09rem !important;
    border: 1.5px solid #d1d5db !important;
    background: #fff !important;
    box-shadow: none !important;
    padding: 0 18px !important;
    transition: border-color 0.2s, box-shadow 0.2s !important;
}
#editClassModal .form-control:focus, #editClassModal .form-select:focus {
    border-color: #2563eb !important;
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.15) !important;
}
#editClassModal .modal-footer {
    padding: 24px 40px 32px 40px !important;
    border-top: 1px solid #e5e7eb !important;
    gap: 16px !important;
    justify-content: center !important;
}
#editClassModal .btn {
    border-radius: 13px !important;
    font-size: 1.13rem !important;
    padding: 12px 32px !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 12px 0 rgba(60,60,90,0.08) !important;
    transition: all 0.2s !important;
    min-width: 100px !important;
}
#editClassModal .btn-primary {
    background: linear-gradient(90deg, #2563eb 0%, #4e73df 100%) !important;
    border: none !important;
}
#editClassModal .btn-primary:hover {
    background: #1746a2 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 16px 0 rgba(37,99,235,0.2) !important;
}
#editClassModal .btn-secondary {
    background: #6b7280 !important;
    border: none !important;
}
#editClassModal .btn-secondary:hover {
    background: #374151 !important;
    transform: translateY(-1px) !important;
}
#editClassModal hr {
    margin: 24px 0 !important;
    border-color: #e5e7eb !important;
    opacity: 0.6 !important;
}

#editClassModal .modal-body .spinner-border {
    width: 2rem;
    height: 2rem;
}
#editClassModal .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}
#editClassModal .form-control:disabled,
#editClassModal .form-select:disabled {
    background-color: #f8f9fa;
    opacity: 0.7;
    cursor: not-allowed;
}

#editClassModal .subject-row {
    background: #f8fafc !important;
    border-radius: 13px !important;
    padding: 20px 24px !important;
    margin-bottom: 16px !important;
    border: 1px solid #e2e8f0 !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06) !important;
    display: flex !important;
    align-items: center !important;
    gap: 16px !important;
    position: relative !important;
    transition: all 0.2s ease;
}
#editClassModal .subject-row:hover {
    background: #f0f9ff !important;
    border-color: #2563eb !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(37,99,235,0.1) !important;
}
#editClassModal .subject-row .form-select {
    border-radius: 10px !important;
    height: 42px !important;
    font-size: 1rem !important;
    border: 1.5px solid #d1d5db !important;
    flex: 1 !important;
    min-width: 0 !important;
}
#editClassModal .subject-row .form-select:focus {
    border-color: #2563eb !important;
    box-shadow: 0 0 0 0.2rem rgba(37,99,235,0.15) !important;
}
#editClassModal .subject-row .form-label {
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    color: #64748b !important;
    margin-bottom: 4px !important;
}

#editClassModal .remove-subject-btn {
    border-radius: 50% !important;
    width: 32px !important;
    height: 32px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: #fef2f2 !important;
    border: 1.5px solid #fecaca !important;
    color: #dc2626 !important;
    transition: all 0.2s ease;
}
#editClassModal .remove-subject-btn:hover {
    background: #dc2626 !important;
    color: white !important;
    transform: scale(1.1) rotate(90deg) !important;
    box-shadow: 0 2px 8px rgba(220,38,38,0.3) !important;
}

#editClassModal #addSubjectBtn {
    border-radius: 13px !important;
    font-size: 1.05rem !important;
    padding: 8px 20px !important;
    font-weight: 600 !important;
    background: #f0f9ff !important;
    border: 1.5px solid #2563eb !important;
    color: #2563eb !important;
    transition: all 0.2s ease;
}
#editClassModal #addSubjectBtn:hover {
    background: #2563eb !important;
    color: white !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(37,99,235,0.2) !important;
}

#editClassModal .form-control.is-invalid,
#editClassModal .form-select.is-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220,53,69,0.15) !important;
}
#editClassModal .invalid-feedback {
    display: block;
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

#editClassModal .btn-primary:disabled {
    background: #6c757d !important;
    border-color: #6c757d !important;
}
#editClassModal .btn-primary .spinner-border {
    width: 1rem;
    height: 1rem;
}

@media (max-width: 767.98px) {
    #editClassModal .modal-dialog {
        max-width: 95vw !important;
        margin: 10px auto !important;
    }
    #editClassModal .modal-header {
        padding: 20px 24px 16px 24px !important;
    }
    #editClassModal .modal-title {
        font-size: 1.4rem !important;
    }
    #editClassModal .modal-body {
        padding: 20px 24px 16px 24px !important;
    }
    #editClassModal .modal-footer {
        padding: 16px 24px 20px 24px !important;
    }
    #editClassModal .form-control, #editClassModal .form-select {
        height: 42px !important;
        font-size: 1rem !important;
    }
    #editClassModal .btn {
        font-size: 1rem !important;
        padding: 10px 24px !important;
    }
}

/* ========== 7. 教师管理 ========== */
/* 教师卡片 */
.teacher-card {
    position: relative;
    z-index: 10;
    background: #fafdff;
    box-shadow: 0 2px 12px 0 rgba(60,60,90,0.06);
    border-radius: 18px;
    border: 1.5px solid #e5e7eb;
    margin-bottom: 22px;
    transition: box-shadow 0.22s, border-color 0.22s, background 0.22s;
    overflow: visible !important;
    padding: 26px 32px 18px 32px;
    min-height: 92px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.teacher-card.selected,
.teacher-card:hover {
    box-shadow: 0 6px 28px 0 rgba(37,99,235,0.13);
    border-color: #2563eb99;
    background: #f4f8ff;
}

/* 教师卡片头部 */
.teacher-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    gap: 12px;
}

.teacher-card-title {
    font-weight: 700;
    font-size: 1.18rem;
    color: #22304a;
    margin-right: 10px;
}

.teacher-card-header .badge {
    margin-right: 8px;
    font-size: 0.98rem;
    font-weight: 500;
    padding: 0.32em 0.9em;
    vertical-align: middle;
}

/* 教师卡片元信息 */
.teacher-card-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 10px 16px;
    margin-bottom: 2px;
}

.teacher-card-meta .badge {
    font-size: 0.97rem;
    padding: 0.28em 0.85em;
    margin-right: 0;
}

/* 教师科目 */
.teacher-card-subjects {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 2px;
}

.teacher-card-subjects .badge {
    font-size: 0.97rem;
    padding: 0.28em 0.85em;
}

/* 教师备注 */
.teacher-card-remark {
    font-size: 0.93rem;
    color: #7b8794;
    font-style: italic;
    margin-top: 2px;
    margin-bottom: 2px;
}

/* 教师卡片操作按钮 */
.teacher-card-actions {
    position: absolute;
    top: 18px;
    right: 22px;
    opacity: 0;
    z-index: 20;
    transition: opacity 0.2s, box-shadow 0.2s;
    display: flex;
    gap: 8px;
    pointer-events: auto;
}

.teacher-card.selected .teacher-card-actions,
.teacher-card:hover .teacher-card-actions {
    opacity: 1 !important;
    pointer-events: auto;
}

.teacher-card-actions .btn {
    border-radius: 8px !important;
    font-size: 0.98rem;
    padding: 3px 14px 3px 10px;
    box-shadow: 0 2px 8px 0 rgba(60,60,90,0.08);
    transition: background 0.18s, color 0.18s, box-shadow 0.18s;
}

.teacher-card-actions .btn-outline-primary:hover {
    background: #2563eb;
    color: #fff;
    border-color: #2563eb;
}

.teacher-card-actions .btn-outline-danger:hover {
    background: #ef4444;
    color: #fff;
    border-color: #ef4444;
}

/* 教师详情模态框 */
.teacher-detail-modal {
    text-align: left;
}

.teacher-detail-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e3e6f0;
}

.teacher-detail-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2e2f37;
}

.teacher-edit-btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.teacher-detail-info-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.teacher-detail-label {
    font-weight: 600;
    color: #5a5c69;
}

.teacher-detail-value {
    color: #2e2f37;
}

.teacher-detail-section {
    margin-bottom: 1.5rem;
}

/* 教师角色和科目徽章 */
.teacher-role-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.teacher-subject-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.teacher-manage-class {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.teacher-teach-classes {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.teacher-remark {
    background: #f8f9fc;
    padding: 0.75rem;
    border-radius: 0.5rem;
    color: #5a5c69;
    font-style: italic;
}

/* 教师添加和编辑模态框 */
.add-teacher-modal .swal2-title,
.edit-teacher-modal .swal2-title {
    color: #2e2f37;
    font-weight: 600;
}

.add-teacher-modal .form-label,
.edit-teacher-modal .form-label {
    font-weight: 500;
    color: #5a5c69;
}

.add-teacher-modal .form-control,
.add-teacher-modal .form-select,
.edit-teacher-modal .form-control,
.edit-teacher-modal .form-select {
    border: 1px solid #d1d3e2;
    border-radius: 0.5rem;
}

.add-teacher-modal .swal2-actions,
.edit-teacher-modal .swal2-actions {
    gap: 0.5rem;
}

/* 教师筛选和搜索 */
#teacherTypeFilter,
#teacherClassFilter,
#teacherSubjectFilter {
    height: 40px;
    border-radius: 12px;
    border: 1.5px solid #d1d5db;
    font-size: 1.08rem;
    color: #22223a;
    background: #fff;
    transition: border-color 0.2s, box-shadow 0.2s;
    box-shadow: none;
    padding: 0 18px;
    margin-right: 8px;
}

#teacherTypeFilter:focus,
#teacherClassFilter:focus,
#teacherSubjectFilter:focus {
    border-color: #2563eb;
    box-shadow: 0 0 0 2px #2563eb22;
    outline: none;
}

#teacherTypeFilter option,
#teacherClassFilter option,
#teacherSubjectFilter option {
    font-size: 1.05rem;
    color: #374151;
    padding: 6px 12px;
}

#teacherTypeFilter:disabled,
#teacherClassFilter:disabled,
#teacherSubjectFilter:disabled {
    background: #f3f4f6;
    color: #b0b3bb;
    border-color: #e5e7eb;
}

#teacherSearchInput {
    height: 40px;
    border-radius: 12px;
    font-size: 1.08rem;
    padding: 0 18px;
}

#addTeacherBtn,
#importTeacherBtn,
.d-flex.align-items-center.gap-2 .btn,
.d-flex.align-items-center.gap-2 button {
    border-radius: 12px !important;
    height: 40px;
    font-size: 1.08rem;
    padding: 0 22px;
}

/* 教师编辑模态框增强样式 */
.edit-teacher-modal .swal2-popup {
    max-width: 700px !important;
    border-radius: 22px !important;
    background: #fafdff !important;
    box-shadow: 0 8px 40px 0 rgba(60,60,90,0.13) !important;
    padding: 44px 48px 36px 48px !important;
}
.edit-teacher-modal .swal2-title {
    font-size: 2.2rem !important;
    font-weight: 800 !important;
    color: #22304a !important;
    margin-bottom: 32px !important;
}
.edit-teacher-modal .form-label {
    font-weight: 700 !important;
    color: #22304a !important;
    font-size: 1.08rem !important;
    margin-bottom: 6px !important;
}
.edit-teacher-modal .form-control, .edit-teacher-modal .form-select {
    border-radius: 13px !important;
    height: 48px !important;
    font-size: 1.09rem !important;
    border: 1.5px solid #d1d5db !important;
    background: #fff !important;
    box-shadow: none !important;
    padding: 0 18px !important;
}
.edit-teacher-modal .swal2-actions {
    margin-top: 32px !important;
    gap: 18px !important;
    justify-content: center !important;
}
.edit-teacher-modal .btn {
    border-radius: 13px !important;
    font-size: 1.13rem !important;
    padding: 10px 38px !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 12px 0 rgba(60,60,90,0.08) !important;
    transition: background 0.18s, color 0.18s, box-shadow 0.18s;
}
.edit-teacher-modal .btn-primary {
    background: linear-gradient(90deg, #2563eb 0%, #4e73df 100%) !important;
    border: none !important;
}
.edit-teacher-modal .btn-primary:hover {
    background: #1746a2 !important;
}
.edit-teacher-modal .btn-secondary {
    background: #6b7280 !important;
    border: none !important;
}
.edit-teacher-modal .btn-secondary:hover {
    background: #374151 !important;
}
.edit-teacher-modal .row.g-3 > .col-12, .edit-teacher-modal .row.g-3 > .col-12.col-md-6 {
    margin-bottom: 18px !important;
}


.add-class-modal .swal2-title {
  font-weight: bold;
  color: #527aaf;
}
.add-class-modal .form-label {
  font-weight: 500;
}
.add-class-modal .form-control, .add-class-modal .form-select {
  border-radius: 0.3rem;
}
.add-class-modal .swal2-actions {
  margin-top: 1.5rem;
}

/* ========== 8. 学生管理 ========== */
/* 学生卡片 */
.student-card {
    position: relative;
    z-index: 10;
    background: #fafdff;
    box-shadow: 0 2px 12px 0 rgba(60,60,90,0.06);
    border-radius: 18px;
    border: 1.5px solid #e5e7eb;
    margin-bottom: 22px;
    transition: box-shadow 0.22s, border-color 0.22s, background 0.22s;
    overflow: visible !important;
    padding: 26px 32px 18px 32px;
    min-height: 92px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.student-card.selected,
.student-card:hover {
    box-shadow: 0 6px 28px 0 rgba(37,99,235,0.13);
    border-color: #2563eb99;
    background: #f4f8ff;
}

/* 学生卡片头部 */
.student-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    gap: 12px;
}

.student-card-title {
    font-weight: 700;
    font-size: 1.18rem;
    color: #22304a;
    margin-right: 10px;
}

.student-card-header .badge {
    margin-right: 8px;
    font-size: 0.98rem;
    font-weight: 500;
    padding: 0.32em 0.9em;
    vertical-align: middle;
}

/* 学生卡片元信息 */
.student-card-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 10px 16px;
    margin-bottom: 2px;
}

.student-card-meta .badge {
    font-size: 0.97rem;
    padding: 0.28em 0.85em;
    margin-right: 0;
}

/* 学生留言信息 */
.student-card-message {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 2px;
}

.student-card-message .badge {
    font-size: 0.97rem;
    padding: 0.28em 0.85em;
}

/* 学生反馈 */
.student-card-feedback {
    font-size: 0.93rem;
    color: #7b8794;
    font-style: italic;
    margin-top: 2px;
    margin-bottom: 2px;
}

/* 学生卡片操作按钮 */
.student-card-actions {
    position: absolute;
    top: 18px;
    right: 22px;
    opacity: 0;
    z-index: 20;
    transition: opacity 0.2s, box-shadow 0.2s;
    display: flex;
    gap: 8px;
    pointer-events: auto;
}

.student-card.selected .student-card-actions,
.student-card:hover .student-card-actions {
    opacity: 1 !important;
    pointer-events: auto;
}

.student-card-actions .btn {
    border-radius: 8px !important;
    font-size: 0.98rem;
    padding: 3px 14px 3px 10px;
    box-shadow: 0 2px 8px 0 rgba(60,60,90,0.08);
    transition: background 0.18s, color 0.18s, box-shadow 0.18s;
}

.student-card-actions .btn-outline-primary:hover {
    background: #2563eb;
    color: #fff;
    border-color: #2563eb;
}

.student-card-actions .btn-outline-danger:hover {
    background: #ef4444;
    color: #fff;
    border-color: #ef4444;
}

/* 学生详情模态框 */
.student-detail-modal {
    text-align: left;
}

.student-detail-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e3e6f0;
}

.student-detail-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2e2f37;
}

.student-edit-btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.student-detail-info-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.student-detail-label {
    font-weight: 600;
    color: #5a5c69;
}

.student-detail-value {
    color: #2e2f37;
}

.student-detail-section {
    margin-bottom: 1.5rem;
}

/* 学生添加和编辑模态框 */
.add-student-modal .swal2-title,
.edit-student-modal .swal2-title {
    color: #2e2f37;
    font-weight: 600;
}

.add-student-modal .form-label,
.edit-student-modal .form-label {
    font-weight: 500;
    color: #5a5c69;
}

.add-student-modal .form-control,
.add-student-modal .form-select,
.edit-student-modal .form-control,
.edit-student-modal .form-select {
    border: 1px solid #d1d3e2;
    border-radius: 0.5rem;
}

.add-student-modal .swal2-actions,
.edit-student-modal .swal2-actions {
    gap: 0.5rem;
}

/* 学生筛选和搜索 */
#studentClassFilter,
#studentGradeFilter {
    height: 40px;
    border-radius: 12px;
    border: 1.5px solid #d1d5db;
    font-size: 1.08rem;
    color: #22223a;
    background: #fff;
    transition: border-color 0.2s, box-shadow 0.2s;
    box-shadow: none;
    padding: 0 18px;
    margin-right: 8px;
}

#studentClassFilter:focus,
#studentGradeFilter:focus {
    border-color: #2563eb;
    box-shadow: 0 0 0 2px #2563eb22;
    outline: none;
}

#studentClassFilter option,
#studentGradeFilter option {
    font-size: 1.05rem;
    color: #374151;
    padding: 6px 12px;
}

#studentClassFilter:disabled,
#studentGradeFilter:disabled {
    background: #f3f4f6;
    color: #b0b3bb;
    border-color: #e5e7eb;
}

#studentSearchInput {
    height: 40px;
    border-radius: 12px;
    font-size: 1.08rem;
    padding: 0 18px;
}
#addStudentBtn,
#importStudentBtn,
.d-flex.align-items-center.gap-2 .btn,
.d-flex.align-items-center.gap-2 button {
    border-radius: 12px !important;
    height: 40px;
    font-size: 1.08rem;
    padding: 0 22px;
}

.edit-student-modal .swal2-popup {
    max-width: 700px !important;
    border-radius: 22px !important;
    background: #fafdff !important;
    box-shadow: 0 8px 40px 0 rgba(60,60,90,0.13) !important;
    padding: 44px 48px 36px 48px !important;
}
.edit-student-modal .swal2-title {
    font-size: 2.2rem !important;
    font-weight: 800 !important;
    color: #22304a !important;
    margin-bottom: 32px !important;
}
.edit-student-modal .form-label {
    font-weight: 700 !important;
    color: #22304a !important;
    font-size: 1.08rem !important;
    margin-bottom: 6px !important;
}
.edit-student-modal .form-control, .edit-student-modal .form-select {
    border-radius: 13px !important;
    height: 48px !important;
    font-size: 1.09rem !important;
    border: 1.5px solid #d1d5db !important;
    background: #fff !important;
    box-shadow: none !important;
    padding: 0 18px !important;
}
.edit-student-modal .swal2-actions {
    margin-top: 32px !important;
    gap: 18px !important;
    justify-content: center !important;
}
.edit-student-modal .btn {
    border-radius: 13px !important;
    font-size: 1.13rem !important;
    padding: 10px 38px !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 12px 0 rgba(60,60,90,0.08) !important;
    transition: background 0.18s, color 0.18s, box-shadow 0.18s;
}
.edit-student-modal .btn-primary {
    background: linear-gradient(90deg, #2563eb 0%, #4e73df 100%) !important;
    border: none !important;
}
.edit-student-modal .btn-primary:hover {
    background: #1746a2 !important;
}
.edit-student-modal .btn-secondary {
    background: #6b7280 !important;
    border: none !important;
}
.edit-student-modal .btn-secondary:hover {
    background: #374151 !important;
}
.edit-student-modal .row.g-3 > .col-12,
.edit-student-modal .row.g-3 > .col-12.col-md-6 {
    margin-bottom: 18px !important;
}

/* ========== 响应式设计 ========== */
/* 平板设备 (991.98px 及以下) */
@media (max-width: 991.98px) {
    /* 图表组件 */
    .grade-avg-chart-card {
        padding: 1.1rem 0.5rem 0.7rem 0.5rem;
    }

    #gradeAvgChartCanvas {
        padding: 0.5rem;
    }

    .grade-info-flex {
        flex-direction: column;
        gap: 1.2rem;
    }

    .grade-info-left,
    .grade-info-right {
        min-width: 0;
        max-width: 100%;
    }

    .grade-info-right .p-2.border.rounded.bg-light {
        min-width: 0;
        max-width: 100%;
        padding: 0.7rem 0.2rem !important;
    }

    /* 可视化图表 */
    .visual-img-box {
        min-width: 120px;
        max-width: 100%;
        padding: 10px 4px 8px 4px;
        min-height: 280px;
    }

    .grade-visualization-row {
        gap: 16px;
        justify-content: center;
        align-items: center;
    }

    .grade-visualization-row .chart-image {
        height: 200px;
    }

    .grade-visualization-row .card-header {
        padding: 0.75rem;
    }

    .grade-visualization-row .card-body {
        padding: 1rem;
    }

    .grade-visualization-row .card-title {
        font-size: 1rem;
    }

    /* 图片查看弹窗 */
    .swal2-image-popup {
        padding: 1.2rem 0.5rem !important;
    }

    .swal2-image {
        max-height: 48vh !important;
    }

    .swal2-image-popup .swal2-title {
        font-size: 1.3rem !important;
    }

    /* 教师和学生卡片 */
    .teacher-card,
    .student-card {
        padding: 1rem;
    }

    .teacher-card-title,
    .student-card-title {
        font-size: 1rem;
    }

    .teacher-detail-modal,
    .student-detail-modal {
        width: 95% !important;
    }

    .teacher-detail-title,
    .student-detail-title {
        font-size: 1.25rem;
    }

    .teacher-detail-info-row,
    .student-detail-info-row {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .teacher-role-badges,
    .teacher-subject-badges,
    .teacher-teach-classes {
        gap: 0.375rem;
    }
}

/* 手机设备 (767.98px 及以下) */
@media (max-width: 767.98px) {
    /* 可视化图表 */
    .grade-visualization-row {
        gap: 12px;
        flex-direction: column;
        align-items: center;
    }

    .grade-visualization-row .chart-image {
        height: 180px;
    }

    .grade-visualization-row .card-header {
        padding: 0.5rem;
    }

    .grade-visualization-row .card-body {
        padding: 0.75rem;
    }

    .grade-visualization-row .card-title {
        font-size: 0.9rem;
    }

    .grade-visualization-row .btn-outline-primary {
        font-size: 0.8rem;
        padding: 0.3rem 0.6rem;
    }

    /* 教师和学生卡片 */
    .teacher-card,
    .student-card {
        padding: 0.75rem;
        margin-bottom: 0.75rem;
    }

    .teacher-card-header,
    .student-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .teacher-card-actions,
    .student-card-actions {
        opacity: 1;
        width: 100%;
        justify-content: flex-end;
    }

    .teacher-detail-header,
    .student-detail-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .teacher-detail-info-row,
    .student-detail-info-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    /* 模态框 */
    .edit-teacher-modal .swal2-popup,
    .edit-student-modal .swal2-popup {
        padding: 18px 6vw 18px 6vw !important;
        max-width: 98vw !important;
    }

    .edit-teacher-modal .swal2-title,
    .edit-student-modal .swal2-title {
        font-size: 1.3rem !important;
    }

    .edit-teacher-modal .form-label,
    .edit-student-modal .form-label {
        font-size: 0.98rem !important;
    }

    .edit-teacher-modal .form-control,
    .edit-teacher-modal .form-select,
    .edit-student-modal .form-control,
    .edit-student-modal .form-select {
        font-size: 0.98rem !important;
        height: 40px !important;
    }

    .edit-teacher-modal .btn,
    .edit-student-modal .btn {
        font-size: 1rem !important;
        padding: 8px 18px !important;
    }

    /* 班级编辑模态框 */
    #editClassModal .modal-dialog {
        max-width: 95vw !important;
        margin: 10px auto !important;
    }

    #editClassModal .modal-header {
        padding: 20px 24px 16px 24px !important;
    }

    #editClassModal .modal-title {
        font-size: 1.4rem !important;
    }

    #editClassModal .modal-body {
        padding: 20px 24px 16px 24px !important;
    }

    #editClassModal .modal-footer {
        padding: 16px 24px 20px 24px !important;
    }

    #editClassModal .form-control,
    #editClassModal .form-select {
        height: 42px !important;
        font-size: 1rem !important;
    }

    #editClassModal .btn {
        font-size: 1rem !important;
        padding: 10px 24px !important;
    }
}
