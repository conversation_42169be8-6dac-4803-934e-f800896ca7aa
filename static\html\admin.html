<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员后台 - 学生成绩管理系统</title>
    <link rel="stylesheet" href="/css/public.css">
    <link rel="stylesheet" href="/css/top.css">
    <link rel="stylesheet" href="/css/admin.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.umd.min.js"></script>
    <style>
    .sidebar-sticky {
        position: sticky;
        top: 80px; /* 适配你的导航栏高度 */
        z-index: 100;
    }

    @media (max-width: 991.98px) {
        .sidebar-sticky {
            position: static;
            top: auto;
        }
    }

    /* 优化教师、学生、班级、年级和考试管理的弹窗样式 */
    .add-teacher-modal, .edit-teacher-modal, .add-student-modal, .edit-student-modal, .add-class-modal, .add-grade-modal, .import-grade-modal, .add-exam-modal, .import-exam-modal {
        border-radius: 12px !important;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
    }

    .add-teacher-modal .swal2-header, .edit-teacher-modal .swal2-header,
    .add-student-modal .swal2-header, .edit-student-modal .swal2-header, .add-class-modal .swal2-header,
    .add-grade-modal .swal2-header, .import-grade-modal .swal2-header,
    .add-exam-modal .swal2-header, .import-exam-modal .swal2-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
        color: white !important;
        border-radius: 12px 12px 0 0 !important;
        padding: 20px 30px !important;
    }

    .add-teacher-modal .swal2-title, .edit-teacher-modal .swal2-title,
    .add-student-modal .swal2-title, .edit-student-modal .swal2-title, .add-class-modal .swal2-title,
    .add-grade-modal .swal2-title, .import-grade-modal .swal2-title,
    .add-exam-modal .swal2-title, .import-exam-modal .swal2-title {
        color: white !important;
        font-weight: 600 !important;
        margin: 0 !important;
    }

    .add-teacher-modal .swal2-content, .edit-teacher-modal .swal2-content,
    .add-student-modal .swal2-content, .edit-student-modal .swal2-content, .add-class-modal .swal2-content,
    .add-grade-modal .swal2-content, .import-grade-modal .swal2-content,
    .add-exam-modal .swal2-content, .import-exam-modal .swal2-content {
        padding: 25px 30px !important;
    }

    .add-teacher-modal .form-label, .edit-teacher-modal .form-label,
    .add-student-modal .form-label, .edit-student-modal .form-label, .add-class-modal .form-label,
    .add-grade-modal .form-label, .import-grade-modal .form-label,
    .add-exam-modal .form-label, .import-exam-modal .form-label {
        font-weight: 500 !important;
        color: #495057 !important;
        margin-bottom: 8px !important;
    }

    .add-teacher-modal .form-control, .edit-teacher-modal .form-control,
    .add-student-modal .form-control, .edit-student-modal .form-control, .add-class-modal .form-control,
    .add-teacher-modal .form-select, .edit-teacher-modal .form-select,
    .add-student-modal .form-select, .edit-student-modal .form-select, .add-class-modal .form-select {
        border: 2px solid #e9ecef !important;
        border-radius: 8px !important;
        transition: all 0.3s ease !important;
        font-size: 14px !important;
    }

    .add-teacher-modal .form-control:focus, .edit-teacher-modal .form-control:focus,
    .add-student-modal .form-control:focus, .edit-student-modal .form-control:focus, .add-class-modal .form-control:focus,
    .add-teacher-modal .form-select:focus, .edit-teacher-modal .form-select:focus,
    .add-student-modal .form-select:focus, .edit-student-modal .form-select:focus, .add-class-modal .form-select:focus {
        border-color: #007bff !important;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
    }

    .add-teacher-modal .swal2-actions, .edit-teacher-modal .swal2-actions,
    .add-student-modal .swal2-actions, .edit-student-modal .swal2-actions, .add-class-modal .swal2-actions {
        padding: 20px 30px !important;
        background-color: #f8f9fa !important;
        border-radius: 0 0 12px 12px !important;
        gap: 15px !important;
    }

    .add-teacher-modal .btn, .edit-teacher-modal .btn,
    .add-student-modal .btn, .edit-student-modal .btn, .add-class-modal .btn {
        border-radius: 8px !important;
        font-weight: 500 !important;
        padding: 10px 25px !important;
        transition: all 0.3s ease !important;
    }

    .add-teacher-modal .btn-primary, .edit-teacher-modal .btn-primary,
    .add-student-modal .btn-primary, .edit-student-modal .btn-primary, .add-class-modal .btn-primary {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
        border: none !important;
    }

    .add-teacher-modal .btn-primary:hover, .edit-teacher-modal .btn-primary:hover,
    .add-student-modal .btn-primary:hover, .edit-student-modal .btn-primary:hover, .add-class-modal .btn-primary:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4) !important;
    }

    .add-teacher-modal .btn-secondary, .edit-teacher-modal .btn-secondary,
    .add-student-modal .btn-secondary, .edit-student-modal .btn-secondary, .add-class-modal .btn-secondary {
        background-color: #6c757d !important;
        border: none !important;
    }

    .add-teacher-modal .btn-secondary:hover, .edit-teacher-modal .btn-secondary:hover,
    .add-student-modal .btn-secondary:hover, .edit-student-modal .btn-secondary:hover, .add-class-modal .btn-secondary:hover {
        background-color: #5a6268 !important;
        transform: translateY(-2px) !important;
    }

    /* 分隔线样式 */
    .add-teacher-modal hr, .edit-teacher-modal hr,
    .add-student-modal hr, .edit-student-modal hr, .add-class-modal hr {
        border-color: #dee2e6 !important;
        margin: 20px 0 !important;
    }

    /* 图标和标题样式 */
    .add-teacher-modal .bi, .edit-teacher-modal .bi,
    .add-student-modal .bi, .edit-student-modal .bi, .add-class-modal .bi {
        font-size: 1.1em !important;
    }

    /* 科目选择器样式 - 仿照任教证级样式 */
    .subject-selector {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
        position: relative;
    }

    .subject-selector:hover {
        border-color: #007bff;
        background-color: #e7f3ff;
    }

    .subject-selector.expanded {
        border-color: #007bff;
        background-color: #e7f3ff;
    }

    .subject-selector-header {
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 500;
        color: #495057;
        padding: 12px 15px;
        min-height: 42px;
        box-sizing: border-box;
    }

    .subject-selector-content {
        padding: 15px;
        border-top: 1px solid #dee2e6;
        background-color: white;
        border-radius: 0 0 6px 6px;
    }

    .subject-radio-group {
        margin-bottom: 15px;
    }

    .subject-radio-group:last-child {
        margin-bottom: 0;
    }

    .subject-type-title {
        font-weight: 600;
        color: #007bff;
        margin-bottom: 10px;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .subject-radio {
        margin-bottom: 8px;
        display: flex;
        align-items: center;
    }

    .subject-radio input[type="radio"] {
        margin-right: 10px;
        transform: scale(1.2);
        accent-color: #007bff;
    }

    .subject-radio label {
        font-weight: 400;
        color: #495057;
        cursor: pointer;
        margin-bottom: 0;
        flex: 1;
        padding: 5px 0;
        transition: color 0.2s ease;
    }

    .subject-radio input[type="radio"]:checked + label {
        color: #007bff;
        font-weight: 500;
    }

    .subject-radio:hover label {
        color: #0056b3;
    }

    /* 新增课程按钮样式 */
    .add-subject-btn {
        width: 100%;
        padding: 8px 12px;
        border: 2px dashed #007bff;
        background-color: transparent;
        color: #007bff;
        border-radius: 6px;
        font-size: 13px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-top: 10px;
    }

    .add-subject-btn:hover {
        background-color: #007bff;
        color: white;
        border-style: solid;
    }

    /* 动态调整的textarea样式 */
    .auto-resize-textarea {
        resize: none;
        overflow: hidden;
        min-height: 42px;
        transition: height 0.2s ease;
    }
    </style>
</head>
<body class="bg-light">
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top shadow-sm">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="#">
                <i class="bi bi-shield-lock me-2"></i>
                <span>管理员后台</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link active" href="#"><i class="bi bi-house-door me-1"></i> 首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="helpBtn"><i class="bi bi-question-circle me-1"></i> 帮助</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="logoutBtn"><i class="bi bi-box-arrow-right me-1"></i> 退出登录</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container main-content mt-4 mb-5 p-4 rounded shadow-sm bg-white animate__animated animate__fadeIn">
        <!-- 欢迎信息 -->
        <div class="welcome-section text-center mb-5 animate__animated animate__fadeIn">
            <h1 class="display-5 fw-bold text-primary">学生成绩管理系统 - 管理员后台</h1>
            <p class="lead text-muted">统一管理教师、学生、班级和年级信息</p>
        </div>
        <div class="row g-4">
            <!-- 左侧功能导航 -->
            <div class="col-lg-3 d-flex flex-column">
                <!-- sticky部分：功能导航 -->
                <div class="sidebar-sticky">
                    <div class="card shadow-sm animate__animated animate__fadeInLeft mb-3">
                        <div class="card-header bg-light">
                            <h5 class="card-title mb-0 d-flex align-items-center">
                                <i class="bi bi-building me-2"></i> 年级管理
                            </h5>
                        </div>
                        <div class="card-body p-3">
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-primary w-100 active" id="gradeTabBtn">
                                    <i class="bi bi-list-ol me-1"></i> 年级列表
                                </button>
                            </div>
                        </div>
                    </div>
                    <hr class="my-2">
                    <div class="card shadow-sm animate__animated animate__fadeInLeft mb-3">
                        <div class="card-header bg-light">
                            <h5 class="card-title mb-0 d-flex align-items-center">
                                <i class="bi bi-people me-2"></i> 班级管理
                            </h5>
                        </div>
                        <div class="card-body p-3">
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-success w-100" id="classTabBtn">
                                    <i class="bi bi-collection me-1"></i> 班级列表
                                </button>
                            </div>
                        </div>
                    </div>
                    <hr class="my-2">
                    <div class="card shadow-sm animate__animated animate__fadeInLeft mb-3">
                        <div class="card-header bg-light">
                            <h5 class="card-title mb-0 d-flex align-items-center">
                                <i class="bi bi-person-badge me-2"></i> 教师管理
                            </h5>
                        </div>
                        <div class="card-body p-3">
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-info w-100" id="teacherTabBtn">
                                    <i class="bi bi-person-video2 me-1"></i> 教师列表
                                </button>
                            </div>
                        </div>
                    </div>
                    <hr class="my-2">
                    <div class="card shadow-sm animate__animated animate__fadeInLeft mb-3">
                        <div class="card-header bg-light">
                            <h5 class="card-title mb-0 d-flex align-items-center">
                                <i class="bi bi-person-lines-fill me-2"></i> 学生管理
                            </h5>
                        </div>
                        <div class="card-body p-3">
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-warning w-100" id="studentTabBtn">
                                    <i class="bi bi-people-fill me-1"></i> 学生列表
                                </button>
                            </div>
                        </div>
                    </div>
                    <hr class="my-2">
                    <div class="card shadow-sm animate__animated animate__fadeInLeft mb-3">
                        <div class="card-header bg-light">
                            <h5 class="card-title mb-0 d-flex align-items-center">
                                <i class="bi bi-clipboard-data me-2"></i> 考试管理
                            </h5>
                        </div>
                        <div class="card-body p-3">
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-danger w-100" id="examTabBtn">
                                    <i class="bi bi-calendar-event me-1"></i> 考试列表
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 右侧主内容区 -->
            <div class="col-lg-9">
                <div id="error-container" class="alert alert-danger d-none animate__animated animate__fadeIn" role="alert"></div>
                <!-- 年级管理面板 -->
                <div id="gradePanel">
                    <!-- 年级选择区域 -->
                    <div class="d-flex align-items-center mb-3">
                        <label class="form-label fw-semibold me-2 mb-0">选择年级：</label>
                        <div id="gradeBtnGroup" class="btn-group" role="group"></div>
                    </div>
                    <!-- 操作按钮和筛选功能区域 -->
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="d-flex align-items-center gap-2">
                            <button class="btn btn-primary" id="addGradeBtn"><i class="bi bi-plus-lg"></i> 新增年级</button>
                            <button class="btn btn-success" id="importGradeBtn"><i class="bi bi-upload"></i> 导入年级</button>
                        </div>
                        <div class="d-flex align-items-center gap-2">
                            <label for="examSelect" class="form-label fw-semibold me-2 mb-0">选择考试：</label>
                            <select id="examSelect" class="form-select w-auto" style="max-width:180px;"></select>
                        </div>
                    </div>
                    <div class="card mb-4 animate__animated animate__fadeIn" id="gradeDetailCard" style="display:none;">
                        <div class="card-body">
                            <!-- 年级名称 -->
                            <div class="grade-header">
                                <h3 id="gradeDetailName" class="fw-bold"></h3>
                            </div>
                            <!-- 统计信息与饼图左右布局 -->
                            <div class="grade-info-flex mb-4" style="display: flex; gap: 2.5rem; flex-wrap: wrap; align-items: stretch;">
                                <!-- 左侧信息 -->
                                <div class="grade-info-left" style="flex: 1 1 320px; min-width: 260px; display: flex; flex-direction: column; gap: 1.5rem;">
                                    <div class="p-2 border rounded bg-light mb-2">
                                        <div class="fw-semibold">班级总数</div>
                                        <div id="gradeClassCount" class="fs-4"></div>
                                    </div>
                                    <div class="p-2 border rounded bg-light">
                                        <div class="fw-semibold">班级类型及分布</div>
                                        <div>
                                            <span class="me-2">普通班：<span id="gradeNormalClassCount"></span></span>
                                            <span class="me-2">实验班：<span id="gradeSpecialClassCount"></span></span>
                                        </div>
                                        <div id="gradeClassTypeDist" class="mt-1"></div>
                                    </div>
                                    <div class="p-2 border rounded bg-light">
                                        <div class="fw-semibold">统计信息</div>
                                        <div>统计人数：<span id="grade-avg-count">--</span></div>
                                        <div>统计班级数：<span id="grade-avg-class-count">--</span></div>
                                    </div>
                                </div>
                                <!-- 右侧饼图 -->
                                <div class="grade-info-right" style="flex: 1 1 260px; min-width: 220px; display: flex; align-items: center; justify-content: center;">
                                    <div class="p-2 border rounded bg-light text-center w-100">
                                        <div class="fw-semibold">班级类型分布饼状图</div>
                                        <canvas id="classTypePieChart" height="220" style="max-width: 220px;"></canvas>
                                    </div>
                                </div>
                            </div>
                            <!-- 年级基本信息表格 -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="table-responsive card p-3">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6 class="mb-0">班级列表</h6>
                                        </div>
                                        <table class="table admin-table table-bordered align-middle text-center">
                                            <thead>
                                                <tr>
                                                    <th>班级</th>
                                                    <th>班主任</th>
                                                    <th>人数</th>
                                                    <th>类型</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody id="gradeClassTableBody">
                                                <tr><td colspan="5" class="text-muted">请选择年级以查看详情</td></tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 班级管理面板 -->
                <div id="classPanel" style="display:none;">
                    <div class="row">
                        <div class="col-md-12" id="classCardListCol">
                            <!-- 年级选择区域 -->
                            <div class="d-flex align-items-center mb-3">
                                <label class="form-label fw-semibold me-2 mb-0">选择年级：</label>
                                <div id="classGradeBtnGroup" class="btn-group" role="group"></div>
                            </div>
                            <!-- 操作按钮和筛选功能区域 -->
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="d-flex align-items-center gap-2">
                                    <button class="btn btn-primary" id="addClassBtn"><i class="bi bi-plus-lg"></i> 新增班级</button>
                                    <button class="btn btn-success" id="importClassBtn"><i class="bi bi-upload"></i> 导入班级</button>
                                </div>
                                <div class="d-flex align-items-center gap-2">
                                    <input type="text" class="form-control w-auto" id="classSearchInput" placeholder="搜索班级" style="max-width:220px;">
                                    <select class="form-select w-auto" id="classTypeFilter" style="max-width:140px;">
                                        <option value="">全部类型</option>
                                        <option value="普通班级">普通班级</option>
                                        <option value="实验班">实验班</option>
                                        <option value="无限制">无限制</option>
                                    </select>
                                </div>
                            </div>
                            <div id="classCardList" class="d-flex flex-column gap-3"></div>
                        </div>
                    </div>
                </div>
                <!-- 教师管理面板 -->
                <div id="teacherPanel" style="display:none;">
                    <div class="row">
                        <div class="col-md-12" id="teacherCardListCol">
                            <!-- 年级选择区域 -->
                            <div class="d-flex align-items-center mb-3">
                                <label class="form-label fw-semibold me-2 mb-0">选择年级：</label>
                                <div id="teacherGradeBtnGroup" class="btn-group" role="group"></div>
                            </div>
                            <!-- 操作按钮和筛选功能区域 -->
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="d-flex align-items-center gap-2">
                                    <button class="btn btn-primary" id="addTeacherBtn"><i class="bi bi-plus-lg"></i> 新增教师</button>
                                    <button class="btn btn-success" id="importTeacherBtn"><i class="bi bi-upload"></i> 导入教师</button>
                                </div>
                                <div class="d-flex align-items-center gap-2">
                                    <input type="text" class="form-control w-auto" id="teacherSearchInput" placeholder="搜索教师" style="max-width:200px;">
                                    <select class="form-select w-auto" id="teacherTypeFilter" style="max-width:140px;">
                                        <option value="">全部类型</option>
                                        <option value="班主任">班主任</option>
                                        <option value="任课教师">任课教师</option>
                                    </select>
                                    <select class="form-select w-auto" id="teacherClassFilter" style="max-width:140px;"></select>
                                    <select class="form-select w-auto" id="teacherSubjectFilter" style="max-width:140px;"></select>
                                </div>
                            </div>
                            <div id="teacherCardList" class="d-flex flex-column gap-3"></div>
                        </div>
                    </div>
                </div>
                <!-- 学生管理面板 -->
                <div id="studentPanel" style="display:none;">
                    <div class="row">
                        <div class="col-md-12" id="studentCardListCol">
                            <!-- 年级选择区域 -->
                            <div class="d-flex align-items-center mb-3">
                                <label class="form-label fw-semibold me-2 mb-0">选择年级：</label>
                                <div id="studentGradeBtnGroup" class="btn-group" role="group"></div>
                            </div>
                            <!-- 操作按钮和筛选功能区域 -->
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div class="d-flex align-items-center gap-2">
                                    <button class="btn btn-primary" id="addStudentBtn"><i class="bi bi-plus-lg"></i> 新增学生</button>
                                    <button class="btn btn-success" id="importStudentBtn"><i class="bi bi-upload"></i> 导入学生</button>
                                </div>
                                <div class="d-flex align-items-center gap-2">
                                    <input type="text" class="form-control w-auto" id="studentSearchInput" placeholder="搜索学生" style="max-width:220px;">
                                    <select class="form-select w-auto" id="studentClassFilter" style="max-width:140px;"></select>
                                </div>
                            </div>
                            <div id="studentCardList" class="d-flex flex-column gap-3"></div>
                        </div>
                    </div>

                    <!-- 考试管理面板 -->
                    <div id="examPanel" style="display:none;">
                        <!-- 年级选择区域 -->
                        <div class="d-flex align-items-center mb-3">
                            <label class="form-label fw-semibold me-2 mb-0">选择年级：</label>
                            <div id="examGradeBtnGroup" class="btn-group" role="group"></div>
                        </div>
                        <!-- 操作按钮和筛选功能区域 -->
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex align-items-center gap-2">
                                <button class="btn btn-primary" id="addExamBtn"><i class="bi bi-plus-lg"></i> 新增考试</button>
                                <button class="btn btn-success" id="importExamBtn"><i class="bi bi-upload"></i> 导入考试</button>
                            </div>
                            <div class="d-flex align-items-center gap-2">
                                <label for="examFilterSelect" class="form-label fw-semibold me-2 mb-0">选择考试：</label>
                                <select id="examFilterSelect" class="form-select w-auto" style="max-width:180px;">
                                    <option value="">请选择考试</option>
                                </select>
                            </div>
                        </div>

                        <!-- 考试详情卡片 -->
                        <div class="card mb-4 animate__animated animate__fadeIn" id="examDetailCard" style="display:none;">
                            <div class="card-body">
                                <!-- 考试名称 -->
                                <div class="exam-header">
                                    <h3 id="examDetailName" class="fw-bold"></h3>
                                    <p id="examDetailInfo" class="text-muted"></p>
                                </div>

                                <!-- 总体成绩变化和平均分展示区域 -->
                                <div class="exam-overview-section mb-4">
                                    <div class="row">
                                        <!-- 左侧：成绩统计信息 -->
                                        <div class="col-md-6">
                                            <div class="card h-100">
                                                <div class="card-header">
                                                    <h6 class="mb-0 d-flex align-items-center">
                                                        <i class="bi bi-bar-chart me-2"></i>成绩统计
                                                    </h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row g-3">
                                                        <div class="col-6">
                                                            <div class="text-center p-3 bg-light rounded">
                                                                <div class="fw-semibold text-muted">参考人数</div>
                                                                <div class="fs-4 fw-bold text-primary" id="examStudentCount">--</div>
                                                            </div>
                                                        </div>
                                                        <div class="col-6">
                                                            <div class="text-center p-3 bg-light rounded">
                                                                <div class="fw-semibold text-muted">参考班级</div>
                                                                <div class="fs-4 fw-bold text-success" id="examClassCount">--</div>
                                                            </div>
                                                        </div>
                                                        <div class="col-12">
                                                            <div class="text-center p-3 bg-light rounded">
                                                                <div class="fw-semibold text-muted">年级总分平均分</div>
                                                                <div class="fs-3 fw-bold text-warning" id="examTotalAvg">--</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- 右侧：成绩趋势图 -->
                                        <div class="col-md-6">
                                            <div class="card h-100">
                                                <div class="card-header">
                                                    <h6 class="mb-0 d-flex align-items-center">
                                                        <i class="bi bi-graph-up me-2"></i>成绩趋势
                                                    </h6>
                                                </div>
                                                <div class="card-body">
                                                    <div id="examTrendContainer" class="text-center">
                                                        <!-- 成绩趋势图将通过JavaScript动态插入 -->
                                                        <div class="text-muted">
                                                            <i class="bi bi-graph-up fs-1"></i>
                                                            <p>选择考试后显示成绩趋势</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 年级总分平均分、年级平均分图表、年级雷达图、年级柱状图、年级成绩表 -->
                                <!-- 年级平均分横向展示区域 -->
                                <div class="grade-avg-section card p-3 mb-4">
                                    <div class="grade-avg-main">
                                        <div class="avg-title">年级总分平均分</div>
                                        <div class="avg-value" id="exam-grade-total-avg">--</div>
                                    </div>
                                    <div class="subject-avg-row" id="exam-grade-subject-avg-row"></div>
                                </div>

                                <!-- 年级平均分统计信息 -->
                                <div class="grade-avg-chart-card mb-4">
                                    <div id="examGradeAvgChartTitle">年级平均分图表</div>
                                    <div id="examGradeAvgChart">
                                        <canvas id="examGradeAvgChartCanvas" height="160"></canvas>
                                    </div>
                                </div>

                                <!-- 年级可视化图片区域 -->
                                <div class="grade-visualization-row mb-4">
                                    <!-- 图表卡片将通过JavaScript动态生成 -->
                                </div>

                                <!-- 年级成绩表 -->
                                <div class="row">
                                    <div class="col-12">
                                        <div class="table-responsive card p-3">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h6 class="mb-0">年级成绩表</h6>
                                            </div>
                                            <table class="table admin-table table-bordered align-middle text-center">
                                                <thead>
                                                    <tr>
                                                        <th>班级</th>
                                                        <th>班主任</th>
                                                        <th>人数</th>
                                                        <th>平均分</th>
                                                        <th>最高分</th>
                                                        <th>最低分</th>
                                                        <th>操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="examGradeTableBody">
                                                    <tr><td colspan="7" class="text-muted">请选择年级和考试以查看详情</td></tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 预留：弹窗/表单区域（如添加/编辑年级、班级、教师、学生等） -->
    <div id="adminModals"></div>

    <!-- 帮助模态框 -->
    <div class="modal fade" id="helpModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-question-circle me-2"></i>使用帮助</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <ul>
                        <li>左侧选择功能分区，右侧进行相应的管理操作</li>
                        <li>支持年级、班级、教师、学生的增删改查</li>
                        <li>支持班级内班主任、任课教师、请假信息等管理</li>
                        <li>支持批量导入导出（如Excel）</li>
                        <li>如遇问题请联系系统管理员</li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增教师 Modal -->
    <div class="modal fade" id="addTeacherModal" tabindex="-1" aria-labelledby="addTeacherModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="addTeacherModalLabel">
                        <i class="bi bi-plus-circle me-2"></i>新增教师
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <form id="addTeacherForm">
                        <div class="row g-3">
                            <div class="col-12 col-md-6">
                                <label class="form-label">姓名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">账号 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="username" required>
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">密码 <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" name="password" required>
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">电话</label>
                                <input type="text" class="form-control" name="phone">
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">任教证级</label>
                                <select class="form-select" name="certificate_level">
                                    <option value="">请选择证级</option>
                                    <option value="初级">初级</option>
                                    <option value="中级">中级</option>
                                    <option value="高级">高级</option>
                                    <option value="特级">特级</option>
                                </select>
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">任教科目</label>
                                <div class="subject-selector" id="addTeacherSubjectSelector">
                                    <div class="subject-selector-header" onclick="toggleAddTeacherSubjectSelector()">
                                        <span>请选择科目</span>
                                        <i class="bi bi-chevron-down" id="addTeacherSubjectSelectorIcon"></i>
                                    </div>
                                    <div class="subject-selector-content" id="addTeacherSubjectSelectorContent" style="display: none;">
                                        <div id="addTeacherSubjectRadios">
                                            <!-- 科目单选框将在这里动态生成 -->
                                        </div>
                                        <button type="button" class="add-subject-btn" onclick="showAddSubjectModal()">
                                            <i class="bi bi-plus-circle me-1"></i> 新增科目
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <label class="form-label">备注</label>
                                <textarea class="form-control auto-resize-textarea" name="remark" placeholder="请输入备注信息..." oninput="autoResizeTextarea(this)"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveAddTeacherBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑教师 Modal -->
    <div class="modal fade" id="editTeacherModal" tabindex="-1" aria-labelledby="editTeacherModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="editTeacherModalLabel">
                        <i class="bi bi-pencil me-2"></i>编辑教师信息
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <form id="editTeacherForm">
                        <div class="row g-3">
                            <div class="col-12 col-md-6">
                                <label class="form-label">姓名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">账号 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="username" required>
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">密码</label>
                                <input type="password" class="form-control" name="password" placeholder="留空则不修改">
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">电话</label>
                                <input type="text" class="form-control" name="phone">
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">任教证级</label>
                                <select class="form-select" name="certificate_level">
                                    <option value="">请选择证级</option>
                                    <option value="初级">初级</option>
                                    <option value="中级">中级</option>
                                    <option value="高级">高级</option>
                                    <option value="特级">特级</option>
                                </select>
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">任教科目</label>
                                <div class="subject-selector" id="editTeacherSubjectSelector">
                                    <div class="subject-selector-header" onclick="toggleEditTeacherSubjectSelector()">
                                        <span>请选择科目</span>
                                        <i class="bi bi-chevron-down" id="editTeacherSubjectSelectorIcon"></i>
                                    </div>
                                    <div class="subject-selector-content" id="editTeacherSubjectSelectorContent" style="display: none;">
                                        <div id="editTeacherSubjectRadios">
                                            <!-- 科目单选框将在这里动态生成 -->
                                        </div>
                                        <button type="button" class="add-subject-btn" onclick="showAddSubjectModal()">
                                            <i class="bi bi-plus-circle me-1"></i> 新增科目
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <label class="form-label">备注</label>
                                <textarea class="form-control auto-resize-textarea" name="remark" placeholder="请输入备注信息..." oninput="autoResizeTextarea(this)"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveEditTeacherBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增学生 Modal -->
    <div class="modal fade" id="addStudentModal" tabindex="-1" aria-labelledby="addStudentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="addStudentModalLabel">
                        <i class="bi bi-plus-circle me-2"></i>新增学生
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <form id="addStudentForm">
                        <div class="row g-3">
                            <div class="col-12 col-md-6">
                                <label class="form-label">姓名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">学号 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="student_id" required>
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">密码 <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" name="password" required>
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">班级 <span class="text-danger">*</span></label>
                                <select class="form-select" name="class_name" required>
                                    <option value="">请选择班级</option>
                                </select>
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">性别</label>
                                <select class="form-select" name="gender">
                                    <option value="">请选择性别</option>
                                    <option value="男">男</option>
                                    <option value="女">女</option>
                                </select>
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">电话</label>
                                <input type="text" class="form-control" name="phone">
                            </div>
                            <div class="col-12">
                                <label class="form-label">教师留言</label>
                                <textarea class="form-control auto-resize-textarea" name="teacher_message" placeholder="请输入教师留言..." oninput="autoResizeTextarea(this)"></textarea>
                            </div>
                            <div class="col-12">
                                <label class="form-label">反馈</label>
                                <textarea class="form-control auto-resize-textarea" name="feedback" placeholder="请输入反馈信息..." oninput="autoResizeTextarea(this)"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveAddStudentBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑学生 Modal -->
    <div class="modal fade" id="editStudentModal" tabindex="-1" aria-labelledby="editStudentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="editStudentModalLabel">
                        <i class="bi bi-pencil me-2"></i>编辑学生信息
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <form id="editStudentForm">
                        <div class="row g-3">
                            <div class="col-12 col-md-6">
                                <label class="form-label">姓名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">学号 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="student_id" required>
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">密码</label>
                                <input type="password" class="form-control" name="password" placeholder="留空则不修改">
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">班级 <span class="text-danger">*</span></label>
                                <select class="form-select" name="class_name" required>
                                    <option value="">请选择班级</option>
                                </select>
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">性别</label>
                                <select class="form-select" name="gender">
                                    <option value="">请选择性别</option>
                                    <option value="男">男</option>
                                    <option value="女">女</option>
                                </select>
                            </div>
                            <div class="col-12 col-md-6">
                                <label class="form-label">电话</label>
                                <input type="text" class="form-control" name="phone">
                            </div>
                            <div class="col-12">
                                <label class="form-label">教师留言</label>
                                <textarea class="form-control auto-resize-textarea" name="teacher_message" placeholder="请输入教师留言..." oninput="autoResizeTextarea(this)"></textarea>
                            </div>
                            <div class="col-12">
                                <label class="form-label">反馈</label>
                                <textarea class="form-control auto-resize-textarea" name="feedback" placeholder="请输入反馈信息..." oninput="autoResizeTextarea(this)"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveEditStudentBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增班级 Modal -->
    <div class="modal fade" id="addClassModal" tabindex="-1" aria-labelledby="addClassModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="addClassModalLabel">
                        <i class="bi bi-plus-circle me-2"></i>新增班级
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <form id="addClassForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">班级名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="class_name" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">班级类型</label>
                                <select class="form-select" name="class_type">
                                    <option value="普通班级">普通班级</option>
                                    <option value="实验班">实验班</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">班主任 <span class="text-danger">*</span></label>
                                <select class="form-select" name="head_teacher" required>
                                    <option value="">请选择班主任</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">人数</label>
                                <input type="number" class="form-control" name="class_size" min="0" placeholder="班级人数">
                            </div>
                            <div class="col-12">
                                <label class="form-label">备注</label>
                                <textarea class="form-control auto-resize-textarea" name="remark" placeholder="请输入备注信息..." oninput="autoResizeTextarea(this)"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveAddClassBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑班级信息 Modal -->
    <div class="modal fade" id="editClassModal" tabindex="-1" aria-labelledby="editClassModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="editClassModalLabel">编辑班级信息</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="关闭"></button>
                </div>
                <div class="modal-body">
                    <form id="editClassForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">班级名称 <span class="text-danger">*</span></label>
                                <label for="editClassName"></label><input type="text" class="form-control" id="editClassName" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">班级类型</label>
                                <label for="editClassType"></label><select class="form-select" id="editClassType">
                                    <option value="普通班级">普通班级</option>
                                    <option value="实验班">实验班</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">班主任 <span class="text-danger">*</span></label>
                                <label for="editHeadTeacher"></label><select class="form-select" id="editHeadTeacher"></select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">人数</label>
                                <label for="editClassSize"></label><input type="number" class="form-control" id="editClassSize" min="0">
                            </div>
                            <div class="col-12">
                                <label class="form-label">备注</label>
                                <label for="editRemark"></label><input type="text" class="form-control" id="editRemark">
                            </div>
                        </div>
                        <hr>
                        <div>
                            <div class="d-flex align-items-center mb-3">
                                <i class="bi bi-person-video2 me-2 text-primary"></i>
                                <label class="form-label mb-0 fw-bold">科目-任课教师分配</label>
                            </div>
                            <div id="subjectTeacherList"></div>
                            <button type="button" class="btn btn-outline-success btn-sm mt-3" id="addSubjectBtn">
                                <i class="bi bi-plus-circle"></i> 添加科目
                            </button>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveClassBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>
    <script src="/js/public.js"></script>
    <script src="/js/top.js"></script>
    <script src="/js/admin.js"></script>
</body>
</html>
