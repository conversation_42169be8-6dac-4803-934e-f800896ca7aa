/**
 * ========================================
 * 管理员页面功能模块 (admin.js)
 * ========================================
 * 专门处理管理员页面的业务逻辑
 * 依赖：public.js（通用工具函数）、top.js（导航和认证）
 *
 * 功能模块分类：
 * 1. 页面初始化和标签页管理
 * 2. 通用工具函数
 * 3. 年级管理模块
 * 4. 班级管理模块
 * 5. 教师管理模块
 * 6. 学生管理模块
 * 7. 图表和数据可视化模块
 * 8. 动画和UI效果模块
 

// ========================================
// 1. 页面初始化和标签页管理模块
// ========================================

/**
 * 页面DOM加载完成后的初始化函数
 * 负责设置标签页、绑定事件、初始化数据
 */
document.addEventListener('DOMContentLoaded', function() {
    // 登录状态检查由 top.js 统一处理，这里只做页面初始化

    initTabManagement();
    initPageComponents();
    initDefaultData();
});

/**
 * 初始化标签页管理系统
 * 设置标签页切换逻辑和事件绑定
 */
function initTabManagement() {
    // 获取所有标签页元素
    const tabElements = getTabElements();
    const { gradeTabBtn, classTabBtn, teacherTabBtn, studentTabBtn, examTabBtn, gradePanel, classPanel, teacherPanel, studentPanel, examPanel } = tabElements;

    /**
     * 标签页切换核心逻辑
     * @param {HTMLElement} tabBtn - 要激活的标签按钮
     * @param {HTMLElement} panel - 要显示的面板
     */
    function setActiveTab(tabBtn, panel) {
        // 重置所有标签页状态
        resetAllTabs(tabElements);

        // 激活当前标签页
        activateTab(tabBtn, panel);

        // 执行特定面板的加载逻辑
        executeTabSpecificLogic(panel, tabElements);
    }

    // 配置标签页和对应的加载函数
    const tabConfigs = [
        { btn: gradeTabBtn, panel: gradePanel, loader: loadGradeList },
        { btn: classTabBtn, panel: classPanel, loader: loadClassGradeList },
        { btn: teacherTabBtn, panel: teacherPanel, loader: loadTeacherGradeList },
        { btn: studentTabBtn, panel: studentPanel, loader: loadStudentGradeList },
        { btn: examTabBtn, panel: examPanel, loader: loadExamGradeList }
    ];

    // 绑定标签页切换事件
    bindTabEvents(tabConfigs, setActiveTab);

    // 默认显示年级管理标签页
    setActiveTab(gradeTabBtn, gradePanel);
    loadGradeList();
}

/**
 * 获取所有标签页相关的DOM元素
 * @returns {Object} 包含所有标签页元素的对象
 */
function getTabElements() {
    return {
        gradeTabBtn: document.getElementById('gradeTabBtn'),
        classTabBtn: document.getElementById('classTabBtn'),
        teacherTabBtn: document.getElementById('teacherTabBtn'),
        studentTabBtn: document.getElementById('studentTabBtn'),
        examTabBtn: document.getElementById('examTabBtn'),
        gradePanel: document.getElementById('gradePanel'),
        classPanel: document.getElementById('classPanel'),
        teacherPanel: document.getElementById('teacherPanel'),
        studentPanel: document.getElementById('studentPanel'),
        examPanel: document.getElementById('examPanel')
    };
}

/**
 * 重置所有标签页状态
 * @param {Object} tabElements - 标签页元素对象
 */
function resetAllTabs(tabElements) {
    const { gradeTabBtn, classTabBtn, teacherTabBtn, studentTabBtn, examTabBtn, gradePanel, classPanel, teacherPanel, studentPanel, examPanel } = tabElements;

    // 移除所有按钮的激活状态
    [gradeTabBtn, classTabBtn, teacherTabBtn, studentTabBtn, examTabBtn].forEach(btn => {
        if (btn) btn.classList.remove('active');
    });

    // 隐藏所有面板
    [gradePanel, classPanel, teacherPanel, studentPanel, examPanel].forEach(panel => {
        if (panel) panel.style.display = 'none';
    });
}

/**
 * 激活指定的标签页
 * @param {HTMLElement} tabBtn - 标签按钮
 * @param {HTMLElement} panel - 面板元素
 */
function activateTab(tabBtn, panel) {
    if (!tabBtn || !panel) return;

    // 激活按钮和面板
    tabBtn.classList.add('active');
    panel.style.display = '';

    // 添加淡入动画
    panel.classList.add('animate__fadeIn');
    setTimeout(() => panel.classList.remove('animate__fadeIn'), 800);

    // 平滑滚动到主内容区顶部
    scrollToMainContent();
}

/**
 * 执行特定标签页的加载逻辑
 * @param {HTMLElement} panel - 当前激活的面板
 * @param {Object} tabElements - 标签页元素对象
 */
function executeTabSpecificLogic(panel, tabElements) {
    const { teacherPanel, studentPanel } = tabElements;

    if (panel === teacherPanel) {
        loadTeacherGradeList();
    } else if (panel === studentPanel) {
        loadStudentGradeList();
    }
}

/**
 * 绑定标签页切换事件
 * @param {Array} tabConfigs - 标签页配置数组
 * @param {Function} setActiveTab - 标签页切换函数
 */
function bindTabEvents(tabConfigs, setActiveTab) {
    tabConfigs.forEach(({ btn, panel, loader }) => {
        if (btn) {
            btn.addEventListener('click', function() {
                setActiveTab(btn, panel);
                if (loader) loader();
            });
        }
    });
}

/**
 * 平滑滚动到主内容区顶部
 */
function scrollToMainContent() {
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
        mainContent.scrollIntoView({behavior: 'smooth', block: 'start'});
    }
}

/**
 * 初始化页面组件
 * 包括科目-教师选择器和新增按钮
 */
function initPageComponents() {
    initSubjectTeacherSelector();
    initAddGradeButton();
    initImportGradeButton();
    initAddExamButton();
    initImportExamButton();
    initAddClassButton();
    initImportClassButton();
    initClassTypeFilter();
}

/**
 * 初始化默认数据
 * 加载年级信息和班级年级列表
 */
function initDefaultData() {
    initGradeData();
    loadClassGradeList();
}

// ========================================
// 2. 通用工具函数模块
// ========================================

/**
 * 初始化科目-教师选择器
 * 设置科目选择时自动加载对应的教师列表
 */
function initSubjectTeacherSelector() {
    // 动态获取年级（优先从页面按钮组获取，否则从sessionStorage）
    let grade = document.querySelector('#gradeBtnGroup .btn.active')?.getAttribute('data-grade');
    if (!grade) {
        grade = sessionStorage.getItem('grade');
    }

    const subjectSelect = document.getElementById('subject-select');
    const teacherSelect = document.getElementById('teacher-select');

    // 判空保护，防止元素不存在时报错
    if (subjectSelect && teacherSelect) {
        subjectSelect.addEventListener('change', async function() {
            const subject = subjectSelect.value;
            if (!subject) {
                teacherSelect.innerHTML = '<option value="">请选择老师</option>';
                return;
            }

            try {
                const data = await apiGet(`/api/subject-teachers?grade=${encodeURIComponent(grade)}&subject=${encodeURIComponent(subject)}`, false);
                if (data.success && data.teachers.length > 0) {
                    teacherSelect.innerHTML = '<option value="">请选择老师</option>';
                    data.teachers.forEach(teacher => {
                        const option = document.createElement('option');
                        option.value = teacher.name;
                        option.textContent = teacher.name;
                        teacherSelect.appendChild(option);
                    });
                } else {
                    teacherSelect.innerHTML = '<option value="">无可选老师</option>';
                }
            } catch (error) {
                teacherSelect.innerHTML = '<option value="">加载失败</option>';
                console.error('获取老师失败', error);
            }
        });
    }
}



/**
 * 初始化年级数据
 * 从API获取年级信息并存储到sessionStorage
 */
async function initGradeData() {
    if (!sessionStorage.getItem('grade')) {
        try {
            const data = await apiGet('/api/exams', false);
            const grades = Array.isArray(data) ? data : (data.grades || []);
            if (grades.length > 0) {
                sessionStorage.setItem('grade', grades.join(','));
            }
        } catch (error) {
            console.warn('获取年级数据失败:', error);
        }
    }
}

/**
 * 获取管理员年级列表
 * @returns {Array} 年级列表
 */
function getAdminGrades() {
    return (sessionStorage.getItem('grade') || '').split(',').filter(Boolean);
}

/**
 * 创建年级按钮组
 * @param {string} containerId - 容器ID
 * @param {Function} onGradeClick - 年级点击回调函数
 * @param {string} activeGrade - 默认激活的年级
 */
function createGradeButtonGroup(containerId, onGradeClick, activeGrade = null) {
    const container = document.getElementById(containerId);
    if (!container) return;

    const adminGrades = getAdminGrades();
    if (!adminGrades.length) {
        container.innerHTML = '<div class="text-danger">未获取到年级数据</div>';
        return;
    }

    // 渲染按钮组
    container.innerHTML = adminGrades.map((grade, index) => {
        const isActive = activeGrade ? grade === activeGrade : index === 0;
        return `<button type="button" class="btn btn-outline-primary${isActive ? ' active' : ''}" data-grade="${grade}">${grade}</button>`;
    }).join('');

    // 绑定点击事件
    Array.from(container.children).forEach(btn => {
        btn.onclick = function() {
            Array.from(container.children).forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            if (onGradeClick) {
                onGradeClick(this.getAttribute('data-grade'));
            }
        };
    });
}

/**
 * 获取当前选中的年级
 * @param {string} containerId - 年级按钮组容器ID
 * @returns {string} 当前选中的年级
 */
function getCurrentGrade(containerId) {
    const activeBtn = document.querySelector(`#${containerId} .btn.active`);
    return activeBtn ? activeBtn.getAttribute('data-grade') : '';
}

/**
 * 显示加载状态
 * @param {string} containerId - 容器ID
 * @param {string} message - 加载消息
 */
function showLoadingState(containerId, message = '加载中...') {
    const container = document.getElementById(containerId);
    if (container) {
        container.innerHTML = `<div class="text-center py-5 text-muted">
            <span class="spinner-border spinner-border-sm"></span> ${message}
        </div>`;
    }
}

/**
 * 显示空状态
 * @param {string} containerId - 容器ID
 * @param {string} message - 空状态消息
 */
function showEmptyState(containerId, message = '暂无数据') {
    const container = document.getElementById(containerId);
    if (container) {
        container.innerHTML = `<div class="text-center py-5 text-muted">${message}</div>`;
    }
}

/**
 * 统一的API错误处理
 * @param {string} containerId - 容器ID
 * @param {string} error - 错误信息
 */
function showApiError(containerId, error) {
    const container = document.getElementById(containerId);
    if (container) {
        container.innerHTML = `<div class="text-center py-5 text-danger">
            <i class="bi bi-exclamation-triangle"></i> ${error}
        </div>`;
    }
}

// ========================================
// 3. 年级管理模块
// ========================================

/**
 * 加载年级列表并渲染年级按钮组
 * 初始化年级管理页面的主要入口函数
 */
function loadGradeList() {
    // 创建年级按钮组，点击时加载对应年级的详细信息
    createGradeButtonGroup('gradeBtnGroup', loadGradeDetailForManagement);

    // 默认加载第一个年级的数据
    const adminGrades = getAdminGrades();
    if (adminGrades.length > 0) {
        loadGradeDetailForManagement(adminGrades[0]);
    }
}

/**
 * 为年级管理加载年级详细信息
 * @param {string} grade - 年级名称
 */
async function loadGradeDetailForManagement(grade) {
    const card = document.getElementById('gradeDetailCard');
    if (!card) return;

    // 显示年级详情卡片并设置标题
    card.style.display = '';
    document.getElementById('gradeDetailName').textContent = grade + ' 年级';

    try {
        // 获取年级下的班级列表用于统计
        const data = await apiGet(`/api/classes?grade=${encodeURIComponent(grade)}`, false);

        if (!data.success) {
            displayGradeDetailError(data.error || '未找到班级数据');
            return;
        }

        const classes = data.classes || [];

        // 更新年级基本统计信息
        updateGradeStatistics(classes);

        // 获取班级详细信息（不需要考试参数）
        await loadGradeClassAnalysis(classes, grade);

        // 添加动画效果
        animateSection('gradeDetailCard');

    } catch (error) {
        console.error('加载年级详情失败:', error);
        displayGradeDetailError('加载年级详情失败');
    }
}





/**
 * 显示年级详情加载错误状态
 * @param {string} errorMessage - 错误消息
 */
function displayGradeDetailError(errorMessage) {
    // 重置统计数字为错误状态
    const errorElements = [
        'gradeClassCount', 'gradeNormalClassCount', 'gradeSpecialClassCount'
    ];
    errorElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) element.textContent = '-';
    });

    // 清空类型分布显示
    const gradeClassTypeDist = document.getElementById('gradeClassTypeDist');
    if (gradeClassTypeDist) gradeClassTypeDist.innerHTML = '';

    // 显示错误信息在表格中
    const tbody = document.getElementById('gradeClassTableBody');
    if (tbody) {
        tbody.innerHTML = `<tr><td colspan="5" class="text-danger">${errorMessage}</td></tr>`;
    }

    // 清空图表
    renderClassTypePieChart({});
    renderGradeAvgChart([]);
    animateSection('gradeDetailCard');
}

/**
 * 更新年级统计信息
 * @param {Array} classes - 班级列表
 */
function updateGradeStatistics(classes) {
    document.getElementById('gradeClassCount').textContent = classes.length;

    // 统计班级类型分布
    const typeCount = {};
    let normalCount = 0, specialCount = 0;

    classes.forEach(cls => {
        const type = cls.class_type || '未知';
        typeCount[type] = (typeCount[type] || 0) + 1;
        if (type === '实验班') specialCount++;
        else normalCount++;
    });

    document.getElementById('gradeNormalClassCount').textContent = normalCount;
    document.getElementById('gradeSpecialClassCount').textContent = specialCount;
    document.getElementById('gradeClassTypeDist').innerHTML = Object.entries(typeCount)
        .map(([k, v]) => `<span class='badge bg-primary me-2'>${k}: ${v}</span>`).join('');

    // 渲染班级类型分布饼图
    renderClassTypePieChart(typeCount);
}

/**
 * 加载年级班级分析数据（用于年级概览）
 * @param {Array} classes - 班级列表
 * @param {string} grade - 年级
 */
async function loadGradeClassAnalysis(classes, grade) {
    const tbody = document.getElementById('gradeClassTableBody');
    if (!tbody) return;

    tbody.innerHTML = '<tr><td colspan="5"><span class="spinner-border spinner-border-sm"></span> 加载中...</td></tr>';

    const classRows = [];

    for (const cls of classes) {
        const cname = cls.class_name;
        const headTeacher = cls.head_teacher || '-';
        const count = cls.class_size || '-';

        // 添加查看详情按钮到操作列
        classRows.push(`<tr>
            <td>${cname}</td>
            <td>${headTeacher}</td>
            <td>${count}</td>
            <td>${cls.class_type || '未知'}</td>
            <td>
                <div class="class-card-actions">
                    <button class="btn btn-outline-info btn-sm me-1" onclick="event.stopPropagation(); showClassDetailFromGrade('${grade}', ${cls.id});"><i class="bi bi-eye"></i> 查看详情</button>
                    <button class="btn btn-outline-primary btn-sm me-1" onclick="event.stopPropagation(); editClass('${grade}', ${cls.id});"><i class="bi bi-pencil"></i> 编辑</button>
                    <button class="btn btn-outline-danger btn-sm" onclick="event.stopPropagation(); deleteClass('${grade}', '${cname}', ${cls.id});"><i class="bi bi-trash"></i> 删除</button>
                </div>
            </td>
        </tr>`);
    }

    tbody.innerHTML = classRows.join('');
}

/**
 * 从年级管理页面显示班级详情
 * @param {string} grade - 年级名称
 * @param {number} classId - 班级ID
 */
window.showClassDetailFromGrade = async function(grade, classId) {
    try {
        // 获取班级详细信息
        const data = await apiGet(`/api/classes/${classId}?grade=${encodeURIComponent(grade)}`, false);
        if (!data.success) {
            Swal.fire('错误', data.error || '未找到班级信息', 'error');
            return;
        }

        // 调用班级详情弹窗
        showClassDetailModal(grade, data.class);
    } catch (error) {
        console.error('获取班级信息失败:', error);
        showErrorToast('获取班级信息失败');
    }
};

// ========================================
// 4. 班级管理模块
// ========================================

/**
 * 加载班级管理页面的年级按钮组
 */
function loadClassGradeList() {
    createGradeButtonGroup('classGradeBtnGroup', loadClassList);

    // 默认加载第一个年级的班级列表
    const adminGrades = getAdminGrades();
    if (adminGrades.length > 0) {
        loadClassList();
    }
}

/**
 * 加载班级列表并渲染
 * 根据当前选中的年级加载对应的班级数据
 */
async function loadClassList() {
    const cardList = document.getElementById('classCardList');
    if (!cardList) return;

    // 显示加载状态
    showLoadingState('classCardList', '加载班级数据...');

    // 获取当前选中的年级
    const gradeBtn = document.querySelector('#classGradeBtnGroup .btn.active');
    const grade = gradeBtn ? gradeBtn.getAttribute('data-grade') : '';

    if (!grade) {
        showEmptyState('classCardList', '请先选择年级');
        return;
    }

    try {
        // 先请求班级数据
        const classData = await apiGet(`/api/classes?grade=${encodeURIComponent(grade)}`, false);

        if (!classData.success) {
            showApiError('classCardList', classData.error || '未找到班级数据');
            return;
        }

        const classes = classData.classes || [];

        // 同时加载班级类型和渲染班级卡片
        await Promise.all([
            loadClassTypes(grade),
            renderClassCards(classes)
        ]);

    } catch (error) {
        console.error('加载班级列表失败:', error);
        showApiError('classCardList', '加载班级数据失败');
    }
}

/**
 * 编辑班级信息
 * 全局函数，供HTML中的onclick事件调用
 * @param {string} grade - 年级名称
 * @param {number} classId - 班级ID
 */
window.editClass = async function(grade, classId) {
    // 关闭当前可能打开的详细信息弹窗
    Swal.close();

    try {
        // 获取班级详细信息
        const data = await apiGet(`/api/classes/${classId}?grade=${encodeURIComponent(grade)}`, false);
        if (!data.success) {
            Swal.fire('错误', data.error || '未找到班级信息', 'error');
            return;
        }

        // 显示编辑班级模态框
        showEditClassModal(grade, data.class);
    } catch (error) {
        console.error('获取班级信息失败:', error);
        showErrorToast('获取班级信息失败');
    }
};

// 编辑班级弹窗渲染和交互逻辑
async function showEditClassModal(grade, cls) {
    try {
        // 显示加载状态
        const modal = new bootstrap.Modal(document.getElementById('editClassModal'));
        modal.show();
        
        // 显示加载提示
        const modalBody = document.querySelector('#editClassModal .modal-body');
        const originalContent = modalBody.innerHTML;
        modalBody.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="text-muted">正在加载班级信息...</p>
            </div>
        `;
        
        // 拉取所有科目、老师、班级现有分配
        const [coursesRes, teachersRes, classTeacherRes] = await Promise.all([
            apiGet(`/api/grade-courses?grade=${encodeURIComponent(grade)}`, false),
            apiGet(`/api/teachers?grade=${encodeURIComponent(grade)}`, false),
            apiGet(`/api/class-teachers?grade=${encodeURIComponent(grade)}&class_name=${encodeURIComponent(cls.class_name)}`, false)
        ]);
        
        // 检查API响应
        if (!coursesRes.success) {
            throw new Error('获取科目信息失败');
        }
        if (!teachersRes.success) {
            throw new Error('获取教师信息失败');
        }
        
        const allCourses = (coursesRes.courses || []).map(c => c.course_name);
        const allTeachers = (teachersRes.teachers || []).map(t => ({...t, subjects: t.subjects || ''}));
        const subjectTeachers = {};
        
        // 处理科目-教师分配数据
        if (classTeacherRes.success && classTeacherRes.subject_teachers) {
            classTeacherRes.subject_teachers.forEach(st => {
                if (st.course_name && st.name) {
                    subjectTeachers[st.course_name] = st.name;
                }
            });
        }
        
        // 在 showEditClassModal 作用域内，记录初始科目
        const initialSubjects = Object.keys(subjectTeachers);
        
        // 恢复原始内容
        modalBody.innerHTML = originalContent;

    // 渲染表单
    document.getElementById('editClassName').value = cls.class_name;
    document.getElementById('editClassType').value = cls.class_type || '普通班级';
    document.getElementById('editClassSize').value = cls.class_size || '';
    document.getElementById('editRemark').value = cls.remark || '';
    // 渲染班主任下拉
    const headTeacherSelect = document.getElementById('editHeadTeacher');
    headTeacherSelect.innerHTML = '<option value="">请选择班主任</option>' +
        allTeachers
            .filter(t => t.is_head_teacher == 0 || t.name === cls.head_teacher)
            .map(t => `<option value="${t.name}"${t.name === cls.head_teacher ? ' selected' : ''}>${t.name}</option>`)
            .join('');
    // 渲染科目-教师分配
    function renderSubjectTeacherList() {
        const listDiv = document.getElementById('subjectTeacherList');
        listDiv.innerHTML = '';
        const usedSubjects = Object.keys(subjectTeachers);

        Object.entries(subjectTeachers).forEach(([subject, teacher]) => {
            const row = document.createElement('div');
            row.className = 'subject-row';

            // 判断是否为初始科目
            const isInitial = initialSubjects.includes(subject);

            // 科目选择区域
            const subjectDiv = document.createElement('div');
            subjectDiv.className = 'flex-grow-1';
            const subjectLabel = document.createElement('label');
            subjectLabel.className = 'form-label';
            subjectLabel.textContent = '科目';
            const subjectSelect = document.createElement('select');
            subjectSelect.className = 'form-select subject-select';
            // 只显示"当前行科目+未分配科目"
            const availableSubjects = [subject, ...allCourses.filter(c => !usedSubjects.includes(c))];
            availableSubjects.forEach(c => {
                const opt = document.createElement('option');
                opt.value = c;
                opt.textContent = c;
                if (c === subject) opt.selected = true;
                subjectSelect.appendChild(opt);
            });
            // 初始科目禁用下拉
            if (isInitial) subjectSelect.disabled = true;
            subjectDiv.appendChild(subjectLabel);
            subjectDiv.appendChild(subjectSelect);

            // 教师选择区域
            const teacherDiv = document.createElement('div');
            teacherDiv.className = 'flex-grow-1';
            const teacherLabel = document.createElement('label');
            teacherLabel.className = 'form-label';
            teacherLabel.textContent = '任课教师';
            const teacherSelect = document.createElement('select');
            teacherSelect.className = 'form-select teacher-select';
            teacherSelect.innerHTML = '<option value="">加载中...</option>';

            async function loadTeachersForSubject(subj, selectedTeacher) {
                try {
                    teacherSelect.innerHTML = '<option value="">加载中...</option>';
                    teacherSelect.disabled = true;

                    const data = await apiGet(`/api/subject-teachers?grade=${encodeURIComponent(grade)}&subject=${encodeURIComponent(subj)}`, false);

                    if (data.success && data.teachers && data.teachers.length > 0) {
                        teacherSelect.innerHTML = '<option value="">请选择老师</option>' +
                            data.teachers.map(t =>
                                `<option value="${t.name}"${t.name === selectedTeacher ? ' selected' : ''}>${t.name}</option>`
                            ).join('');
                    } else {
                        teacherSelect.innerHTML = '<option value="">无可选老师</option>';
                    }

                    teacherSelect.disabled = false;
                } catch (error) {
                    console.error(`加载${subj}科目教师失败:`, error);
                    teacherSelect.innerHTML = '<option value="">加载失败</option>';
                    teacherSelect.disabled = false;
                }
            }
            loadTeachersForSubject(subject, teacher);
            teacherDiv.appendChild(teacherLabel);
            teacherDiv.appendChild(teacherSelect);

            // 删除按钮区域
            const removeDiv = document.createElement('div');
            removeDiv.className = 'd-flex align-items-end';
            const removeBtn = document.createElement('button');
            removeBtn.className = 'remove-subject-btn';
            removeBtn.type = 'button';
            removeBtn.innerHTML = '<i class="bi bi-x-lg"></i>';
            removeBtn.title = '删除此科目';
            removeBtn.onclick = async () => {
                // 如果是初始科目，需要确认
                if (isInitial) {
                    const result = await Swal.fire({
                        title: '确认删除',
                        text: `确定要删除科目"${subject}"的任课教师分配吗？`,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonText: '删除',
                        cancelButtonText: '取消',
                        reverseButtons: true
                    });
                    
                    if (!result.isConfirmed) return;
                }
                
                delete subjectTeachers[subject];
                renderSubjectTeacherList();
                
                // 显示删除成功提示
                const toast = Swal.mixin({
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 2000,
                    timerProgressBar: true
                });
                toast.fire({
                    icon: 'success',
                    title: `已删除科目：${subject}`
                });
            };
            removeDiv.appendChild(removeBtn);

            // 只有新添加的科目才允许切换
            if (!isInitial) {
                subjectSelect.onchange = function() {
                    const newSubject = this.value;
                    if (newSubject === subject) return;
                    if (usedSubjects.includes(newSubject)) {
                        this.value = subject;
                        return;
                    }
                    subjectTeachers[newSubject] = '';
                    delete subjectTeachers[subject];
                    renderSubjectTeacherList();
                };
            }

            // 教师切换
            teacherSelect.onchange = function() {
                subjectTeachers[subject] = this.value;
            };

            row.appendChild(subjectDiv);
            row.appendChild(teacherDiv);
            row.appendChild(removeDiv);
            listDiv.appendChild(row);
        });
    }
    // 添加科目按钮
    document.getElementById('addSubjectBtn').onclick = function() {
        const unused = allCourses.filter(c => !Object.keys(subjectTeachers).includes(c));
        if (unused.length > 0) {
            subjectTeachers[unused[0]] = '';
            renderSubjectTeacherList();
            
            // 显示添加成功提示
            const addedSubject = unused[0];
            const toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 2000,
                timerProgressBar: true
            });
            toast.fire({
                icon: 'success',
                title: `已添加科目：${addedSubject}`
            });
        } else {
            // 显示所有科目都已分配
            Swal.fire({
                title: '提示',
                text: '所有科目都已分配完毕',
                icon: 'info',
                timer: 2000,
                showConfirmButton: false
            });
        }
    };
    renderSubjectTeacherList();
    // 保存按钮事件
    document.getElementById('saveClassBtn').onclick = async function() {
        // 获取表单数据
        const formData = {
            className: document.getElementById('editClassName').value.trim(),
            classType: document.getElementById('editClassType').value,
            classSize: document.getElementById('editClassSize').value,
            headTeacher: document.getElementById('editHeadTeacher').value,
            remark: document.getElementById('editRemark').value
        };

        // 使用 public.js 中的验证函数
        const validationRules = {
            className: {
                required: true,
                label: '班级名称',
                maxLength: 50
            },
            headTeacher: {
                required: true,
                label: '班主任'
            },
            classSize: {
                type: 'number',
                label: '班级人数',
                min: 0,
                max: 100
            }
        };

        const validation = validateForm(validationRules, formData);
        if (!validation.isValid) {
            Swal.fire('提示', validation.errors[0], 'warning');
            return;
        }

        // 验证科目-教师分配
        const invalidSubjects = Object.entries(subjectTeachers).filter(([, teacher]) => !teacher);
        if (invalidSubjects.length > 0) {
            Swal.fire('提示', `以下科目未选择任课教师：${invalidSubjects.map(([subject]) => subject).join('、')}`, 'warning');
            return;
        }

        // 显示保存确认
        const result = await Swal.fire({
            title: '确认保存',
            text: `确定要保存班级"${formData.className}"的信息吗？`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: '保存',
            cancelButtonText: '取消',
            reverseButtons: true
        });

        if (!result.isConfirmed) return;

        // 显示保存中状态
        const saveBtn = document.getElementById('saveClassBtn');
        const originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>保存中...';
        saveBtn.disabled = true;

        try {
            const classId = cls.id;
            const requestData = {
                grade,
                class_name: formData.className,
                class_type: formData.classType,
                class_size: formData.classSize,
                head_teacher: formData.headTeacher,
                remark: formData.remark,
                subject_teachers: subjectTeachers
            };

            const data = await apiPut(`/api/classes/${classId}`, requestData, false);

            if (data.success) {
                showSuccessToast('班级信息已更新');

                // 关闭弹窗并刷新列表
                bootstrap.Modal.getInstance(document.getElementById('editClassModal')).hide();
                loadClassList();

                // 如果当前在年级详情页面，也刷新年级信息
                const gradeDetailCard = document.getElementById('gradeDetailCard');
                if (gradeDetailCard && gradeDetailCard.style.display !== 'none') {
                    const activeGradeBtn = document.querySelector('#gradeBtnGroup .btn.active');
                    if (activeGradeBtn) {
                        const activeGrade = activeGradeBtn.getAttribute('data-grade');
                        if (activeGrade) {
                            loadGradeDetailForManagement(activeGrade);
                        }
                    }
                }
            } else {
                throw new Error(data.error || '保存失败');
            }
        } catch (error) {
            console.error('保存班级信息失败:', error);
            showErrorToast(`保存失败: ${error.message}`);
        } finally {
            // 恢复按钮状态
            saveBtn.innerHTML = originalText;
            saveBtn.disabled = false;
        }
    };

    } catch (error) {
        console.error('加载班级编辑信息失败:', error);
        Swal.fire('错误', `加载班级信息失败: ${error.message}`, 'error');
        bootstrap.Modal.getInstance(document.getElementById('editClassModal')).hide();
        return;
    }
    
    // 取消按钮事件
    document.getElementById('editClassModal').addEventListener('hidden.bs.modal', function () {
        // 清理表单数据
        document.getElementById('editClassName').value = '';
        document.getElementById('editClassType').value = '普通班级';
        document.getElementById('editClassSize').value = '';
        document.getElementById('editHeadTeacher').innerHTML = '<option value="">请选择班主任</option>';
        document.getElementById('editRemark').value = '';
        document.getElementById('subjectTeacherList').innerHTML = '';
    });
}

/**
 * 删除班级
 * 全局函数，供HTML中的onclick事件调用
 * @param {string} grade - 年级名称
 * @param {string} className - 班级名称
 * @param {number} classId - 班级ID
 */
window.deleteClass = async function(grade, className, classId) {
    // 显示删除确认对话框
    const result = await Swal.fire({
        title: '确认删除',
        text: `确定要删除班级：${grade} - ${className} 吗？`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        reverseButtons: true
    });

    if (!result.isConfirmed) return;

    try {
        // 调用删除API
        const data = await apiDelete(`/api/classes/${classId}?grade=${encodeURIComponent(grade)}`);
        if (data.success) {
            showSuccessToast('班级已删除');
            // 刷新班级列表
            loadClassList();
        } else {
            throw new Error(data.error || '删除失败');
        }
    } catch (error) {
        console.error('删除班级失败:', error);
        showErrorToast(`删除失败: ${error.message}`);
    }
};

/**
 * 新增年级
 * 绑定到新增年级按钮的点击事件
 */
function initAddGradeButton() {
    const addGradeBtn = document.getElementById('addGradeBtn');
    if (addGradeBtn) {
        addGradeBtn.onclick = function() {
            Swal.fire({
                title: '新增年级',
                html: `
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label">年级名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="gradeName" placeholder="例如：2024级、高一、初三等" required>
                        </div>
                        <div class="col-12">
                            <label class="form-label">年级描述</label>
                            <textarea class="form-control" id="gradeDescription" placeholder="请输入年级描述信息..." rows="3"></textarea>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: '保存',
                cancelButtonText: '取消',
                customClass: {
                    popup: 'add-grade-modal'
                },
                preConfirm: () => {
                    const gradeName = document.getElementById('gradeName').value.trim();
                    if (!gradeName) {
                        Swal.showValidationMessage('请输入年级名称');
                        return false;
                    }
                    return {
                        name: gradeName,
                        description: document.getElementById('gradeDescription').value.trim()
                    };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    // 调用后端API创建年级
                    fetch('/api/grades', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(result.value)
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire('成功', data.message, 'success').then(() => {
                                // 刷新年级列表
                                location.reload(); // 简单刷新页面以更新年级列表
                            });
                        } else {
                            Swal.fire('错误', data.error || '创建年级失败', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('创建年级失败:', error);
                        Swal.fire('错误', '创建年级失败，请稍后重试', 'error');
                    });
                }
            });
        };
    }
}

/**
 * 导入年级
 * 绑定到导入年级按钮的点击事件
 */
function initImportGradeButton() {
    const importGradeBtn = document.getElementById('importGradeBtn');
    if (importGradeBtn) {
        importGradeBtn.onclick = function() {
            Swal.fire({
                title: '导入年级',
                html: `
                    <div class="mb-3">
                        <label class="form-label">选择Excel文件</label>
                        <input type="file" class="form-control" id="gradeFileInput" accept=".xlsx,.xls" />
                    </div>
                    <div class="alert alert-info">
                        <small>
                            <strong>文件格式要求：</strong><br>
                            • 支持 .xlsx 和 .xls 格式<br>
                            • 第一行为表头：年级名称、年级描述<br>
                            • 年级名称不能重复<br>
                            • 年级描述可以为空
                        </small>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: '开始导入',
                cancelButtonText: '取消',
                customClass: {
                    popup: 'import-grade-modal'
                },
                preConfirm: () => {
                    const fileInput = document.getElementById('gradeFileInput');
                    if (!fileInput.files.length) {
                        Swal.showValidationMessage('请选择要导入的文件');
                        return false;
                    }
                    return fileInput.files[0];
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    // 这里可以添加实际的文件上传和导入逻辑
                    Swal.fire('提示', '导入年级功能开发中，敬请期待！', 'info');
                }
            });
        };
    }
}

/**
 * 新增考试
 * 绑定到新增考试按钮的点击事件
 */
function initAddExamButton() {
    const addExamBtn = document.getElementById('addExamBtn');
    if (addExamBtn) {
        addExamBtn.onclick = function() {
            const gradeBtn = document.querySelector('#examGradeBtnGroup .btn.active');
            const grade = gradeBtn ? gradeBtn.getAttribute('data-grade') : '';
            if (!grade) {
                Swal.fire('提示', '请先选择年级', 'info');
                return;
            }

            Swal.fire({
                title: '新增考试',
                html: `
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label">考试名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="examName" placeholder="例如：第一次月考、期中考试等" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">考试日期 <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="examDate" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">考试类型</label>
                            <select class="form-select" id="examType">
                                <option value="月考">月考</option>
                                <option value="期中考试">期中考试</option>
                                <option value="期末考试">期末考试</option>
                                <option value="模拟考试">模拟考试</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">考试描述</label>
                            <textarea class="form-control" id="examDescription" placeholder="请输入考试描述信息..." rows="3"></textarea>
                        </div>
                        <div class="col-12">
                            <div class="alert alert-info">
                                <small><strong>当前年级：</strong>${grade}</small>
                            </div>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: '保存',
                cancelButtonText: '取消',
                customClass: {
                    popup: 'add-exam-modal'
                },
                preConfirm: () => {
                    const examName = document.getElementById('examName').value.trim();
                    const examDate = document.getElementById('examDate').value;
                    if (!examName) {
                        Swal.showValidationMessage('请输入考试名称');
                        return false;
                    }
                    if (!examDate) {
                        Swal.showValidationMessage('请选择考试日期');
                        return false;
                    }
                    return {
                        name: examName,
                        date: examDate,
                        type: document.getElementById('examType').value,
                        description: document.getElementById('examDescription').value.trim(),
                        grade: grade
                    };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    // 调用后端API创建考试
                    fetch('/api/exam-management', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(result.value)
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire('成功', data.message, 'success').then(() => {
                                // 刷新考试列表
                                loadExamListForManagement();
                            });
                        } else {
                            Swal.fire('错误', data.error || '创建考试失败', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('创建考试失败:', error);
                        Swal.fire('错误', '创建考试失败，请稍后重试', 'error');
                    });
                }
            });
        };
    }
}

/**
 * 导入考试
 * 绑定到导入考试按钮的点击事件
 */
function initImportExamButton() {
    const importExamBtn = document.getElementById('importExamBtn');
    if (importExamBtn) {
        importExamBtn.onclick = function() {
            const gradeBtn = document.querySelector('#examGradeBtnGroup .btn.active');
            const grade = gradeBtn ? gradeBtn.getAttribute('data-grade') : '';
            if (!grade) {
                Swal.fire('提示', '请先选择年级', 'info');
                return;
            }

            Swal.fire({
                title: '导入考试',
                html: `
                    <div class="mb-3">
                        <label class="form-label">选择Excel文件</label>
                        <input type="file" class="form-control" id="examFileInput" accept=".xlsx,.xls" />
                    </div>
                    <div class="alert alert-info">
                        <small>
                            <strong>文件格式要求：</strong><br>
                            • 支持 .xlsx 和 .xls 格式<br>
                            • 第一行为表头：考试名称、考试日期、考试类型、考试描述<br>
                            • 考试日期格式：YYYY-MM-DD<br>
                            • 考试类型可选：月考、期中考试、期末考试、模拟考试、其他<br>
                            • 当前选择年级：<strong>${grade}</strong><br>
                            • 考试数据将导入到 <strong>${grade}</strong> 年级数据库中
                        </small>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: '开始导入',
                cancelButtonText: '取消',
                customClass: {
                    popup: 'import-exam-modal'
                },
                preConfirm: () => {
                    const fileInput = document.getElementById('examFileInput');
                    if (!fileInput.files.length) {
                        Swal.showValidationMessage('请选择要导入的文件');
                        return false;
                    }
                    return fileInput.files[0];
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    // 这里可以添加实际的文件上传和导入逻辑
                    Swal.fire('提示', `导入考试功能开发中，将导入到${grade}年级数据库，敬请期待！`, 'info');
                }
            });
        };
    }
}

/**
 * 新增班级
 * 绑定到新增班级按钮的点击事件
 */
function initAddClassButton() {
    const addClassBtn = document.getElementById('addClassBtn');
    if (addClassBtn) {
        addClassBtn.onclick = function() {
            const gradeBtn = document.querySelector('#gradeBtnGroup .btn.active');
            const grade = gradeBtn ? gradeBtn.getAttribute('data-grade') : '';
            if (!grade) {
                Swal.fire('提示', '请先选择年级', 'info');
                return;
            }

            showAddClassModal(grade);
        };
    }
}

/**
 * 导入班级
 * 绑定到导入班级按钮的点击事件
 */
function initImportClassButton() {
    const importClassBtn = document.getElementById('importClassBtn');
    if (importClassBtn) {
        importClassBtn.onclick = function() {
            const gradeBtn = document.querySelector('#gradeBtnGroup .btn.active');
            const grade = gradeBtn ? gradeBtn.getAttribute('data-grade') : '';
            if (!grade) {
                Swal.fire('提示', '请先选择年级', 'info');
                return;
            }

            Swal.fire({
                title: '导入班级',
                html: `
                    <div class="mb-3">
                        <label class="form-label">选择Excel文件</label>
                        <input type="file" class="form-control" id="classFileInput" accept=".xlsx,.xls" />
                    </div>
                    <div class="alert alert-info">
                        <small>
                            <strong>文件格式要求：</strong><br>
                            • 支持 .xlsx 和 .xls 格式<br>
                            • 第一行为表头：班级名称、班级类型、班主任、人数、备注<br>
                            • 班级类型可填写"普通班级"或"实验班"<br>
                            • 班主任必须是 <strong>${grade}</strong> 年级中已存在的教师姓名<br>
                            • 当前选择年级：<strong>${grade}</strong><br>
                            • 班级数据将导入到 <strong>${grade}</strong> 年级数据库中
                        </small>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: '开始导入',
                cancelButtonText: '取消',
                preConfirm: () => {
                    const fileInput = document.getElementById('classFileInput');
                    if (!fileInput.files.length) {
                        Swal.showValidationMessage('请选择要导入的文件');
                        return false;
                    }
                    return fileInput.files[0];
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    // 这里可以添加实际的文件上传和导入逻辑
                    // 传递年级参数到后端
                    Swal.fire('提示', `导入功能开发中，将导入到${grade}年级数据库，敬请期待！`, 'info');
                }
            });
        };
    }
}

/**
 * 显示新增班级模态框
 */
function showAddClassModal(grade) {
    const modal = new bootstrap.Modal(document.getElementById('addClassModal'));
    const form = document.getElementById('addClassForm');

    // 重置表单
    form.reset();

    // 加载班主任选项
    loadTeachersForClass(grade, form.head_teacher);

    // 绑定保存按钮事件
    const saveBtn = document.getElementById('saveAddClassBtn');
    saveBtn.onclick = async function() {
        const formData = {
            class_name: form.class_name.value.trim(),
            head_teacher: form.head_teacher.value,
            class_size: form.class_size.value,
            class_type: form.class_type.value,
            remark: form.remark.value.trim()
        };

        // 验证必填字段
        if (!formData.class_name) {
            Swal.fire('提示', '班级名称不能为空', 'warning');
            return;
        }
        if (!formData.head_teacher) {
            Swal.fire('提示', '请选择班主任', 'warning');
            return;
        }

        // 显示保存中状态
        const originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>保存中...';
        saveBtn.disabled = true;

        try {
            const data = await apiPost('/api/classes', {grade, ...formData});
            if (data.success) {
                showSuccessToast('班级添加成功');
                modal.hide();
                loadClassList();
            } else {
                throw new Error(data.error || '添加失败');
            }
        } catch (error) {
            console.error('添加班级失败:', error);
            showErrorToast(`添加失败: ${error.message}`);
        } finally {
            // 恢复按钮状态
            saveBtn.innerHTML = originalText;
            saveBtn.disabled = false;
        }
    };

    modal.show();
}

/**
 * 为班级表单加载教师选项
 */
async function loadTeachersForClass(grade, selectElement) {
    try {
        const response = await fetch(`/api/teachers?grade=${encodeURIComponent(grade)}`);
        const data = await response.json();

        // 清空现有选项（保留默认选项）
        selectElement.innerHTML = '<option value="">请选择班主任</option>';

        if (data.success && data.teachers) {
            data.teachers.forEach(teacher => {
                const option = document.createElement('option');
                option.value = teacher.name;
                option.textContent = teacher.name;
                selectElement.appendChild(option);
            });
        }
    } catch (error) {
        console.error('加载教师列表失败:', error);
    }
}

/**
 * 初始化班级类型筛选器
 * 动态加载班级类型选项并绑定筛选事件
 */
function initClassTypeFilter() {
    const classTypeFilter = document.getElementById('classTypeFilter');
    const classSearchInput = document.getElementById('classSearchInput');

    if (classTypeFilter) {
        // 绑定班级类型筛选事件
        classTypeFilter.addEventListener('change', function() {
            filterAndRenderClasses();
        });
    }

    if (classSearchInput) {
        // 绑定搜索输入事件
        classSearchInput.addEventListener('input', function() {
            filterAndRenderClasses();
        });
    }
}

/**
 * 加载班级类型选项
 * 根据当前选中的年级动态加载班级类型
 */
async function loadClassTypes(grade) {
    const classTypeFilter = document.getElementById('classTypeFilter');
    if (!classTypeFilter || !grade) return;

    try {
        const data = await apiGet(`/api/class-types?grade=${encodeURIComponent(grade)}`, false);
        if (data.success && data.class_types) {
            // 清空现有选项，保留"全部类型"选项
            classTypeFilter.innerHTML = '<option value="">全部类型</option>';

            // 添加动态加载的班级类型选项
            data.class_types.forEach(type => {
                const option = document.createElement('option');
                option.value = type;
                option.textContent = type;
                classTypeFilter.appendChild(option);
            });
        }
    } catch (error) {
        console.error('加载班级类型失败:', error);
        // 如果加载失败，使用默认选项
        classTypeFilter.innerHTML = `
            <option value="">全部类型</option>
            <option value="普通班级">普通班级</option>
            <option value="实验班">实验班</option>
        `;
    }
}

/**
 * 筛选并渲染班级列表
 * 根据班级类型和搜索关键词筛选班级
 */
function filterAndRenderClasses() {
    const classTypeFilter = document.getElementById('classTypeFilter');
    const classSearchInput = document.getElementById('classSearchInput');

    if (!classTypeFilter || !classSearchInput) {
        console.log('筛选器元素未找到');
        return;
    }

    const selectedType = classTypeFilter.value;
    const searchKeyword = classSearchInput.value.trim().toLowerCase();

    console.log('筛选条件:', { selectedType, searchKeyword });

    // 获取当前所有班级数据
    const allClassCards = document.querySelectorAll('#classCardList .class-card');
    console.log('找到班级卡片数量:', allClassCards.length);

    allClassCards.forEach((card, index) => {
        // 根据实际的HTML结构获取数据
        const classTypeElement = card.querySelector('.badge.bg-primary');
        const classType = classTypeElement ? classTypeElement.textContent.trim() : '';

        const classNameElement = card.querySelector('.class-card-title');
        const className = classNameElement ? classNameElement.textContent.trim() : '';

        const headTeacherElement = card.querySelector('.badge.bg-success');
        const headTeacher = headTeacherElement ? headTeacherElement.textContent.replace('班主任:', '').trim() : '';

        console.log(`班级 ${index + 1}:`, { className, classType, headTeacher });

        // 检查类型筛选
        const typeMatch = !selectedType || classType.includes(selectedType);

        // 检查搜索关键词
        const searchMatch = !searchKeyword ||
            className.toLowerCase().includes(searchKeyword) ||
            headTeacher.toLowerCase().includes(searchKeyword) ||
            classType.toLowerCase().includes(searchKeyword);

        console.log(`班级 ${className} 匹配结果:`, { typeMatch, searchMatch });

        // 显示或隐藏班级卡片
        if (typeMatch && searchMatch) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

/**
 * 渲染年级平均分柱状图
 * @param {Array} classChartData - 班级图表数据数组，包含className和avgScore
 */
function renderGradeAvgChart(classChartData) {
    const chartDiv = document.getElementById('gradeAvgChart');
    if (!chartDiv) {
        console.warn('gradeAvgChart element not found');
        return;
    }

    // 重新创建canvas元素
    chartDiv.innerHTML = '<canvas id="gradeAvgChartCanvas" height="160"></canvas>';

    if (!window.Chart) {
        console.warn('Chart.js not loaded');
        return;
    }

    const canvas = document.getElementById('gradeAvgChartCanvas');
    if (!canvas) {
        console.warn('gradeAvgChartCanvas element not found');
        return;
    }

    const ctx = canvas.getContext('2d');

    // 销毁之前的图表实例
    if (window._gradeAvgChart) {
        window._gradeAvgChart.destroy();
    }

    // 如果没有数据，显示空状态
    if (!classChartData || classChartData.length === 0) {
        chartDiv.innerHTML = '<div class="text-muted text-center py-3">暂无数据</div>';
        return;
    }

    // 兼容旧的数据格式（纯数字数组）
    let labels, data;
    if (typeof classChartData[0] === 'object' && classChartData[0].className) {
        // 新格式：包含班级名称的对象数组
        labels = classChartData.map(item => item.className);
        data = classChartData.map(item => item.avgScore);
    } else {
        // 旧格式：纯数字数组，使用通用标签
        labels = classChartData.map((_, i) => `班级${i + 1}`);
        data = classChartData;
    }

    // 创建新的柱状图
    window._gradeAvgChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: '班级平均分',
                data: data,
                backgroundColor: '#c1d7f6',
                borderColor: '#527aaf',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '平均分'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: '班级'
                    },
                    ticks: {
                        maxRotation: 45,
                        minRotation: 0
                    }
                }
            }
        }
    });

    // 添加动画效果
    animateSection('gradeAvgChart');
}

// 渲染年级平均分和各科平均分（横向卡片）
function renderGradeAverages(class_avg_data) {
    // 总分平均分
    let totalAvg = class_avg_data['总分'];
    document.getElementById('grade-total-avg').innerHTML = 
        (totalAvg !== undefined && totalAvg !== null && totalAvg !== '') 
            ? `${parseFloat(totalAvg).toFixed(1)}<span class="subject-avg-unit">分</span>` 
            : '--';

    // 各科平均分
    const subjects = ['语文', '数学', '英语', '物理', '化学', '生物'];
    let rowHtml = '';
    subjects.forEach(subj => {
        let val = class_avg_data[subj];
        rowHtml += `<div class="subject-avg-card">
            <div class="subject-avg-label">${subj}</div>
            <div class="subject-avg-value">${(val !== undefined && val !== null && val !== '') ? parseFloat(val).toFixed(1) : '--'}</div>
            <div class="subject-avg-unit">分</div>
        </div>`;
    });
    document.getElementById('grade-subject-avg-row').innerHTML = rowHtml;

    // 统计信息
    if (class_avg_data['统计人数']) {
        document.getElementById('grade-avg-count').innerText = class_avg_data['统计人数'];
    }
    if (class_avg_data['统计班级数']) {
        document.getElementById('grade-avg-class-count').innerText = class_avg_data['统计班级数'];
    }

    const hasData = subjects.some(subj => class_avg_data[subj] !== undefined && class_avg_data[subj] !== null && class_avg_data[subj] !== '');
    const emptyElement = document.getElementById('grade-avg-empty');
    if (emptyElement) {
        emptyElement.style.display = hasData ? 'none' : '';
    }
    animateSection('gradeAvgChart');
}

// 重复的函数定义已移除，使用班级管理模块中的统一定义

// 详细信息弹窗优化
async function showClassDetailModal(grade, cls) {
    // 获取任课老师、请假人数
    const [teacherRes, leaveRes] = await Promise.all([
        apiGet(`/api/class-teachers?grade=${encodeURIComponent(grade)}&class_name=${encodeURIComponent(cls.class_name)}`, false),
        apiGet(`/api/leaves?grade=${encodeURIComponent(grade)}&class_name=${encodeURIComponent(cls.class_name)}`, false)
    ]);
    // 获取考试列表
    const examListRes = await apiGet(`/api/grade-exams?grade=${encodeURIComponent(grade)}`, false);
    const exams = examListRes.exams || [];
    // 默认选中第一个考试
    let selectedExam = exams[0] || '';
    // 获取平均分
    let avgData = null;
    if (selectedExam) {
        const avgRes = await apiPost('/generate_charts', {
            grade, exam: selectedExam, class_name: cls.class_name
        }, false);
        avgData = avgRes.class_avg_data || null;
    }
    // 渲染详细信息
    let html = `
      <div class="class-detail-modal">
        <div class="class-detail-header">
          <span class="class-detail-title">${cls.class_name}</span>
          <button class="btn btn-outline-primary btn-sm class-edit-btn" onclick="editClass('${grade}', ${cls.id})">
            <i class="bi bi-pencil"></i> 编辑
          </button>
        </div>
        <div class="class-detail-section">
          <div class="class-detail-label mb-1"><i class="bi bi-person-video2 me-1 text-primary"></i>任课老师：</div>
          <div class="subject-teacher-list">
            ${(teacherRes.success && teacherRes.subject_teachers && teacherRes.subject_teachers.length)
              ? teacherRes.subject_teachers.map(t => `<span class='subject-teacher-item subject-teacher-badge'>${t.course_name}<span class='subject-teacher-name'>${t.name}</span></span>`).join(' ')
              : '<span class="text-muted">--</span>'}
          </div>
        </div>
        <div class="class-detail-info-row">
          <div><span class="class-detail-label">班级类型：</span><span class="class-detail-value">${cls.class_type || '-'}</span></div>
          <div><span class="class-detail-label">班主任：</span><span class="class-detail-value">${cls.head_teacher || '-'}</span></div>
          <div><span class="class-detail-label">人数：</span><span class="class-detail-value">${cls.class_size || '-'}</span></div>
          <div><span class="class-detail-label">请假人数：</span><span class="class-detail-value">${leaveRes.success && leaveRes.data ? leaveRes.data.length : 0}</span></div>
        </div>
        <div class="class-detail-section">
          <div class="class-detail-label mb-1">选择考试：</div>
          <select class="form-select d-inline-block class-detail-exam-select" id="classDetailExamSelectModal">
            ${exams.map(e => `<option value="${e}">${e}</option>`).join('')}
          </select>
        </div>
        <div class="class-detail-section">
          <div class="class-detail-avg-title">${selectedExam ? selectedExam + ' 平均总分：' : '无考试数据'}<span class="class-detail-avg-value">${avgData && avgData['总分'] ? avgData['总分'].toFixed(1) : '--'}</span></div>
          <div class="class-detail-subject-avg">
            ${['语文','数学','英语','物理','化学','生物'].map(subj => `
              <div class='class-detail-subject-avg-item'>
                <div class='class-detail-subject-avg-label'>${subj}</div>
                <div class='class-detail-subject-avg-value'>${avgData && avgData[subj] !== undefined ? avgData[subj].toFixed(1) : '--'}</div>
              </div>
            `).join('')}
          </div>
        </div>
      </div>
    `;
    Swal.fire({
        title: '班级详细信息',
        html,
        width: 940,
        showCloseButton: true,
        showCancelButton: false,
        showConfirmButton: false,
        customClass: {popup: 'class-detail-modal'},
        didOpen: () => {
            // 绑定考试选择事件
            const examSelect = document.getElementById('classDetailExamSelectModal');
            if (examSelect) {
                examSelect.onchange = async function() {
                    const exam = this.value;
                    const avgDiv = document.querySelector('.class-detail-avg-title .class-detail-avg-value');
                    if (avgDiv) avgDiv.innerHTML = '<span class="spinner-border spinner-border-sm"></span>';

                    try {
                        // 重新获取平均分
                        const avgRes = await apiPost('/generate_charts', {
                            grade, exam, class_name: cls.class_name
                        }, false);
                        const avgData = avgRes.class_avg_data || null;

                        // 更新分数
                        const avgTitle = document.querySelector('.class-detail-avg-title');
                        if (avgTitle) avgTitle.innerHTML = exam + ' 平均总分：' + `<span class='class-detail-avg-value'>${avgData && avgData['总分'] ? avgData['总分'].toFixed(1) : '--'}</span>`;

                        // 各科
                        const subjectAvgDivs = document.querySelectorAll('.class-detail-subject-avg-item');
                        ['语文','数学','英语','物理','化学','生物'].forEach((subj, idx) => {
                            const val = avgData && avgData[subj] !== undefined ? avgData[subj].toFixed(1) : '--';
                            if (subjectAvgDivs[idx]) {
                                subjectAvgDivs[idx].querySelector('.class-detail-subject-avg-value').textContent = val;
                            }
                        });
                    } catch (error) {
                        console.error('获取平均分失败:', error);
                        if (avgDiv) avgDiv.textContent = '--';
                    }
                };
            }
        }
    });
}

// ========================================
// 5. 教师管理模块
// ========================================

/**
 * 加载教师列表并设置筛选功能
 * 根据当前选中的年级加载对应的教师数据，并提供搜索和筛选功能
 */
async function loadTeacherList() {
    const cardList = document.getElementById('teacherCardList');
    if (!cardList) return;

    // 显示加载状态
    showLoadingState('teacherCardList', '加载教师数据...');

    // 获取筛选器元素
    const searchInput = document.getElementById('teacherSearchInput');
    const typeFilter = document.getElementById('teacherTypeFilter');
    const classFilter = document.getElementById('teacherClassFilter');
    const subjectFilter = document.getElementById('teacherSubjectFilter');

    // 获取当前选中的年级
    const gradeBtn = document.querySelector('#teacherGradeBtnGroup .btn.active');
    const grade = gradeBtn ? gradeBtn.getAttribute('data-grade') : '';

    try {
        // 构建请求URL
        let url = '/api/teachers';
        if (grade) url += `?grade=${encodeURIComponent(grade)}`;

        // 请求教师数据
        const data = await apiGet(url, false);
        if (!data.success) {
            showApiError('teacherCardList', data.error || '未找到教师数据');
            return;
        }

        const teachers = data.teachers || [];

        // 定义搜索和筛选功能
        function filterAndRender() {
            let filtered = teachers;
            const searchVal = searchInput ? searchInput.value.trim() : '';
            const typeVal = typeFilter ? typeFilter.value : '';
            const classVal = classFilter ? classFilter.value : '';
            const subjectVal = subjectFilter ? subjectFilter.value : '';

            // 按姓名、用户名、电话、管理班级、教授科目、证级搜索
            if (searchVal) {
                filtered = filtered.filter(teacher =>
                    teacher.name.includes(searchVal) ||
                    teacher.username.includes(searchVal) ||
                    (teacher.phone && teacher.phone.includes(searchVal)) ||
                    (teacher.manage_class && teacher.manage_class.includes(searchVal)) ||
                    (teacher.is_class_teacher && teacher.is_class_teacher.includes(searchVal)) ||
                    (teacher.subjects && teacher.subjects.includes(searchVal)) ||
                    (teacher.certificate_level && teacher.certificate_level.includes(searchVal))
                );
            }

            // 按教师类型筛选
            if (typeVal) {
                filtered = filtered.filter(teacher => {
                    if (typeVal === '班主任') {
                        return teacher.is_head_teacher == 1;
                    } else if (typeVal === '任课教师') {
                        return teacher.is_class_teacher && teacher.is_class_teacher !== '0' && teacher.is_class_teacher !== '';
                    }
                    return false;
                });
            }

            // 按班级筛选
            if (grade && classVal) {
                filtered = filtered.filter(teacher => {
                    return teacher.manage_class === classVal ||
                           (teacher.is_class_teacher && teacher.is_class_teacher.includes(classVal));
                });
            }

            // 按科目筛选
            if (subjectVal) {
                filtered = filtered.filter(teacher =>
                    teacher.subjects && teacher.subjects.includes(subjectVal)
                );
            }

            // 渲染筛选后的教师卡片
            renderTeacherCards(filtered);
        }

        // 绑定筛选事件
        if (searchInput) searchInput.oninput = filterAndRender;
        if (typeFilter) typeFilter.onchange = filterAndRender;
        if (classFilter) classFilter.onchange = filterAndRender;
        if (subjectFilter) subjectFilter.onchange = filterAndRender;

        // 加载筛选选项
        loadTeacherFilters(grade);

        // 初始渲染所有教师
        renderTeacherCards(teachers);

    } catch (error) {
        console.error('加载教师数据失败:', error);
        showApiError('teacherCardList', `加载教师数据失败: ${error.message}`);
    }
}

// 加载教师筛选选项
async function loadTeacherFilters(grade) {
    const classFilter = document.getElementById('teacherClassFilter');
    const subjectFilter = document.getElementById('teacherSubjectFilter');

    if (classFilter) {
        if (!grade) {
            classFilter.innerHTML = '<option value="">全部班级</option>';
            classFilter.disabled = true;
        } else {
            classFilter.disabled = false;
            try {
                const data = await apiGet(`/api/classes?grade=${encodeURIComponent(grade)}`, false);
                if (data.success && data.classes) {
                    const classes = data.classes.map(c => c.class_name);
                    classFilter.innerHTML = `
                        <option value="">全部班级</option>
                        ${classes.map(c => `<option value="${c}">${c}</option>`).join('')}
                    `;
                }
            } catch (error) {
                console.error('加载班级筛选选项失败:', error);
            }
        }
    }

    if (subjectFilter) {
        // 从API获取科目列表
        try {
            // 获取当前选中的年级
            const gradeBtn = document.querySelector('#teacherGradeBtnGroup .btn.active');
            const currentGrade = gradeBtn ? gradeBtn.getAttribute('data-grade') : '高一';

            const response = await fetch(`/api/grade/${currentGrade}/courses`);
            const data = await response.json();
            const subjects = data.success ? data.courses.map(c => c.course_name) : ['语文', '数学', '英语', '物理', '化学', '生物'];

            subjectFilter.innerHTML = `
                <option value="">全部科目</option>
                ${subjects.map(s => `<option value="${s}">${s}</option>`).join('')}
            `;
        } catch (error) {
            console.error('获取科目列表失败:', error);
            // 使用默认科目列表
            const subjects = ['语文', '数学', '英语', '物理', '化学', '生物'];
            subjectFilter.innerHTML = `
                <option value="">全部科目</option>
                ${subjects.map(s => `<option value="${s}">${s}</option>`).join('')}
            `;
        }
    }
}

// 渲染教师卡片
async function renderTeacherCards(teachers) {
    const cardList = document.getElementById('teacherCardList');
    if (!cardList) return;
    if (!teachers.length) {
        cardList.innerHTML = '<div class="teacher-card-empty">暂无教师</div>';
        return;
    }
    cardList.innerHTML = '';
    const gradeBtn = document.querySelector('#teacherGradeBtnGroup .btn.active');
    const grade = gradeBtn ? gradeBtn.getAttribute('data-grade') : '';
    for (const teacher of teachers) {
        const card = document.createElement('div');
        card.className = 'teacher-card position-relative';
        card.dataset.teacherId = teacher.id;
        // 角色标签
        const roleBadges = [];
        if (teacher.is_head_teacher == 1) {
            roleBadges.push('<span class="badge bg-success">班主任</span>');
        }
        if ((teacher.is_class_teacher || '') !== '0' && (teacher.is_class_teacher || '') !== '') {
            roleBadges.push('<span class="badge bg-info">任课教师</span>');
        }
        if (!roleBadges.length) {
            roleBadges.push('<span class="badge bg-secondary">普通教师</span>');
        }
        // 管理班级
        let manageClassText = '';
        if (teacher.is_head_teacher == 1 && (teacher.manage_class || '') !== '0' && (teacher.manage_class || '') !== '') {
            manageClassText = `<span class="badge bg-warning text-dark">管理: ${(teacher.manage_class || '')}</span>`;
        }
        // 任教班级
        let teachClassesText = '';
        if ((teacher.is_class_teacher || '') !== '0' && (teacher.is_class_teacher || '') !== '') {
            const classes = (teacher.is_class_teacher || '').split(',').filter(c => c && c !== '0');
            if (classes.length > 0) {
                teachClassesText = `<span class="badge bg-primary">任教: ${classes.join(', ')}</span>`;
            }
        }
        // 编辑/删除按钮（确保只渲染一次且不被其它内容覆盖）
        // 注意：不要把actions变量插入到其它div内部，否则会被flex布局或overflow隐藏
        card.innerHTML = `
            <div class="teacher-card-header d-flex align-items-center justify-content-between">
                <div>
                    <span class="teacher-card-title">${teacher.name || ''}</span>
                    <span class="badge bg-secondary ms-2">${teacher.username || ''}</span>
                    ${roleBadges.join(' ')}
                </div>
            </div>
            <div class="teacher-card-meta mt-1">
                <span class="badge bg-info">电话: ${teacher.phone || '-'}</span>
                <span class="badge bg-warning text-dark ms-2">证级: ${teacher.certificate_level || '-'}</span>
                ${manageClassText}
                ${teachClassesText}
            </div>
            <div class="teacher-card-subjects mt-2">
                <span class="badge bg-primary">科目: ${(teacher.subjects || '未分配')}</span>
            </div>
            ${(teacher.remark || '') ? `<div class="teacher-card-remark mt-1"><small class="text-muted">备注: ${teacher.remark}</small></div>` : ''}
        `;
        // 操作按钮单独append，避免被其它内容覆盖
        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'teacher-card-actions';
        actionsDiv.innerHTML = `
            <button class="btn btn-outline-primary btn-sm me-1" onclick="event.stopPropagation(); editTeacher('${grade}', ${teacher.id});"><i class="bi bi-pencil"></i> 编辑</button>
            <button class="btn btn-outline-danger btn-sm" onclick="event.stopPropagation(); deleteTeacher('${grade}', '${teacher.name}', ${teacher.id});"><i class="bi bi-trash"></i> 删除</button>
        `;
        card.appendChild(actionsDiv);
        // 鼠标悬停高亮和显示操作按钮
        card.onmouseenter = function() {
            card.classList.add('selected');
        };
        card.onmouseleave = function() {
            card.classList.remove('selected');
        };
        // 点击卡片弹窗显示详细信息
        card.onclick = function() {
            document.querySelectorAll('.teacher-card').forEach(c => c.classList.remove('selected'));
            card.classList.add('selected');
            showTeacherDetailModal(grade, teacher);
        };
        cardList.appendChild(card);
    }
}

// 新增教师
window.addTeacher = async function() {
    const gradeBtn = document.querySelector('#gradeBtnGroup .btn.active');
    const grade = gradeBtn ? gradeBtn.getAttribute('data-grade') : '';

    if (!grade) {
        Swal.fire('提示', '请先选择年级', 'info');
        return;
    }

    // 显示新增教师模态框
    showAddTeacherModal(grade);

}

/**
 * 显示新增教师模态框
 */
function showAddTeacherModal(grade) {
    const modal = new bootstrap.Modal(document.getElementById('addTeacherModal'));

    // 重置表单
    const form = document.getElementById('addTeacherForm');
    form.reset();

    // 加载科目数据
    loadSubjectsForAddTeacher(grade);

    // 绑定保存按钮事件
    const saveBtn = document.getElementById('saveAddTeacherBtn');
    saveBtn.onclick = async function() {
        const formData = {
            name: form.name.value.trim(),
            username: form.username.value.trim(),
            password: form.password.value,
            phone: form.phone.value.trim(),
            certificate_level: form.certificate_level.value,
            remark: form.remark.value.trim()
        };

        // 验证必填字段
        if (!formData.name) {
            Swal.fire('提示', '姓名不能为空', 'warning');
            return;
        }
        if (!formData.username) {
            Swal.fire('提示', '账号不能为空', 'warning');
            return;
        }
        if (!formData.password) {
            Swal.fire('提示', '密码不能为空', 'warning');
            return;
        }

        // 获取选中的科目
        const selectedSubject = getAddTeacherSelectedSubject();
        if (!selectedSubject) {
            Swal.fire('提示', '请选择任教科目', 'warning');
            return;
        }

        formData.subjects = selectedSubject;

        // 显示保存中状态
        const originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>保存中...';
        saveBtn.disabled = true;

        try {
            const data = await apiPost('/api/teachers', {grade, ...formData});
            if (data.success) {
                showSuccessToast('教师添加成功');
                modal.hide();
                loadTeacherList();
            } else {
                throw new Error(data.error || '添加失败');
            }
        } catch (error) {
            console.error('添加教师失败:', error);
            showErrorToast(`添加失败: ${error.message}`);
        } finally {
            // 恢复按钮状态
            saveBtn.innerHTML = originalText;
            saveBtn.disabled = false;
        }
    };

    modal.show();
};

// 编辑教师
window.editTeacher = async function(grade, teacherId) {
    try {
        const data = await apiGet(`/api/teachers/${teacherId}?grade=${encodeURIComponent(grade)}`, false);
        if (!data.success) {
            Swal.fire('错误', data.error || '未找到教师信息', 'error');
            return;
        }
        showEditTeacherModal(grade, data.teacher);
    } catch (error) {
        console.error('获取教师信息失败:', error);
        showErrorToast('获取教师信息失败');
    }
};

/**
 * 显示编辑教师模态框
 */
function showEditTeacherModal(grade, teacher) {
    const modal = new bootstrap.Modal(document.getElementById('editTeacherModal'));
    const form = document.getElementById('editTeacherForm');

    // 填充表单数据
    form.name.value = teacher.name || '';
    form.username.value = teacher.username || '';
    form.password.value = '';
    form.phone.value = teacher.phone || '';
    form.certificate_level.value = teacher.certificate_level || '';
    form.remark.value = teacher.remark || '';

    // 加载科目数据并设置选中状态
    loadSubjectsForEditTeacher(grade, teacher.subjects);

    // 绑定保存按钮事件
    const saveBtn = document.getElementById('saveEditTeacherBtn');
    saveBtn.onclick = async function() {
        const formData = {
            name: form.name.value.trim(),
            username: form.username.value.trim(),
            password: form.password.value,
            phone: form.phone.value.trim(),
            certificate_level: form.certificate_level.value,
            remark: form.remark.value.trim()
        };

        // 验证必填字段
        if (!formData.name) {
            Swal.fire('提示', '姓名不能为空', 'warning');
            return;
        }
        if (!formData.username) {
            Swal.fire('提示', '账号不能为空', 'warning');
            return;
        }

        // 获取选中的科目
        const selectedSubject = getEditTeacherSelectedSubject();
        if (!selectedSubject) {
            Swal.fire('提示', '请选择任教科目', 'warning');
            return;
        }

        formData.subjects = selectedSubject;

        // 显示保存中状态
        const originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>保存中...';
        saveBtn.disabled = true;

        try {
            const response = await fetch(`/api/teachers/${teacher.id}`, {
                method: 'PUT',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({grade, ...formData})
            });
            const data = await response.json();

            if (data.success) {
                showSuccessToast('教师信息已保存');
                modal.hide();
                loadTeacherList();
            } else {
                throw new Error(data.error || '保存失败');
            }
        } catch (error) {
            console.error('保存教师信息失败:', error);
            showErrorToast(`保存失败: ${error.message}`);
        } finally {
            // 恢复按钮状态
            saveBtn.innerHTML = originalText;
            saveBtn.disabled = false;
        }
    };

    modal.show();
}

// 删除教师
window.deleteTeacher = function(grade, teacherName, teacherId) {
    Swal.fire({
        title: '确认删除',
        text: `确定要删除教师：${teacherName} 吗？`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: '删除',
        cancelButtonText: '取消'
    }).then(result => {
        if (result.isConfirmed) {
            fetch(`/api/teachers/${teacherId}?grade=${encodeURIComponent(grade)}`, {
                method: 'DELETE'
            })
            .then(res => res.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('成功', '教师已删除', 'success');
                    loadTeacherList();
                } else {
                    Swal.fire('错误', data.error || '删除失败', 'error');
                }
            });
        }
    });
}

// 教师详细信息弹窗
async function showTeacherDetailModal(grade, teacher) {
    // 如果teacher没有密码字段，则通过API获取
    if (typeof teacher.password === 'undefined') {
        const res = await fetch(`/api/teachers/${teacher.id}?grade=${encodeURIComponent(grade)}`);
        const data = await res.json();
        if (data.success && data.teacher) {
            teacher = data.teacher;
        }
    }
    // 获取班级信息
    const classRes = await fetch(`/api/classes?grade=${encodeURIComponent(grade)}`).then(r => r.json());
    const classes = classRes.success ? classRes.classes : [];
    // 解析任教班级
    let teachClasses = [];
    if (teacher.is_class_teacher && teacher.is_class_teacher !== '0' && teacher.is_class_teacher !== '') {
        teachClasses = teacher.is_class_teacher.split(',').filter(c => c && c !== '0');
    }
    // 解析科目
    const subjects = teacher.subjects ? teacher.subjects.split(',') : [];
    // 弹窗内容：左右两列布局，右上角有编辑和删除按钮
    let html = `
        <div class="teacher-detail-modal" style="max-width:100%;overflow-x:hidden;font-size:1.13rem;line-height:2.1;background:#f8f9fb;border-radius:18px;box-shadow:0 8px 32px 0 rgba(60,60,90,0.18);padding:44px 60px 40px 60px;">
            <div class="d-flex justify-content-between align-items-center mb-4" style="min-height:48px;">
                <div class="d-flex align-items-center">
                    <h2 class="fw-bold mb-0 me-3" style="font-size:2.1rem;color:#2d3a4a;"><i class="bi bi-person-badge me-2 text-primary"></i>${teacher.name}</h2>
                </div>
                <div>
                    <button class="btn btn-outline-primary btn-sm me-2" id="editTeacherDetailBtn" style="font-size:1.1rem;padding:6px 20px;border-radius:8px;"><i class="bi bi-pencil"></i> 编辑</button>
                    <button class="btn btn-outline-danger btn-sm" id="deleteTeacherDetailBtn" style="font-size:1.1rem;padding:6px 20px;border-radius:8px;"><i class="bi bi-trash"></i> 删除</button>
                </div>
            </div>
            <div class="row mb-4 g-3 flex-wrap" style="font-size:1.08rem;">
                <div class="col-md-6 mb-2" style="word-break:break-all;">
                    <span style="color:#22304a;font-weight:600;"><i class="bi bi-person-circle me-1" style="color:#3b82f6;"></i>账号：</span><span style="color:#4b5563;font-weight:500;">${teacher.username || '-'}</span>
                </div>
                <div class="col-md-6 mb-2" style="word-break:break-all;">
                    <span style="color:#22304a;font-weight:600;"><i class="bi bi-telephone me-1" style="color:#22c55e;"></i>电话：</span><span style="color:#4b5563;font-weight:500;">${teacher.phone || '-'}</span>
                </div>
                <div class="col-md-6 mb-2">
                    <span style="color:#22304a;font-weight:600;"><i class="bi bi-award me-1" style="color:#f59e42;"></i>证级：</span><span style="color:#4b5563;font-weight:500;">${teacher.certificate_level || '-'}</span>
                </div>
                <div class="col-md-6 mb-2" style="word-break:break-all;">
                    <span style="color:#22304a;font-weight:600;"><i class="bi bi-key me-1" style="color:#64748b;"></i>密码：</span><span style="color:#4b5563;font-weight:500;">${teacher.password ? teacher.password : '<span class=\"text-muted\">（无）</span>'}</span>
                </div>
            </div>
            <hr style="margin:16px 0 20px 0;border-color:#e5e7eb;"/>
            <div class="row mb-4 g-3 flex-wrap" style="font-size:1.08rem;">
                <div class="col-md-6 mb-2">
                    <span style="color:#22304a;font-weight:600;"><i class="bi bi-person-lines-fill me-1" style="color:#2563eb;"></i>角色：</span>
                    <span style="font-weight:500;">
                    ${teacher.is_head_teacher == 1 ? '<span class="badge bg-success me-1">班主任</span>' : ''}
                    ${teacher.is_class_teacher && teacher.is_class_teacher !== '0' && teacher.is_class_teacher !== '' ? '<span class="badge bg-info me-1">任课教师</span>' : ''}
                    ${!teacher.is_head_teacher && (!teacher.is_class_teacher || teacher.is_class_teacher === '0' || teacher.is_class_teacher === '') ? '<span class="badge bg-secondary">普通教师</span>' : ''}
                    </span>
                </div>
                <div class="col-md-6 mb-2">
                    <span style="color:#22304a;font-weight:600;"><i class="bi bi-journal-text me-1" style="color:#ef4444;"></i>科目：</span>
                    <span style="font-weight:500;">
                    ${subjects.length > 0 ? subjects.map(s => `<span class="badge bg-primary me-1">${s}</span>`).join(' ') : '<span class="text-muted">未分配科目</span>'}
                    </span>
                </div>
            </div>
            <hr style="margin:16px 0 20px 0;border-color:#e5e7eb;"/>
            <div class="row mb-4 g-3 flex-wrap" style="font-size:1.08rem;">
                <div class="col-md-6 mb-2">
                    <span style="color:#22304a;font-weight:600;"><i class="bi bi-people-fill me-1" style="color:#fbbf24;"></i>管理班级：</span>
                    <span style="font-weight:500;">
                    ${teacher.is_head_teacher == 1 && teacher.manage_class && teacher.manage_class !== '0' ? `<span class="badge bg-warning text-dark">${teacher.manage_class}</span>` : '<span class="text-muted">无</span>'}
                    </span>
                </div>
                <div class="col-md-6 mb-2">
                    <span style="color:#22304a;font-weight:600;"><i class="bi bi-collection me-1" style="color:#06b6d4;"></i>任教班级：</span>
                    <span style="font-weight:500;">
                    ${teachClasses.length > 0 ? teachClasses.map(c => `<span class="badge bg-info me-1">${c}</span>`).join(' ') : '<span class="text-muted">无</span>'}
                    </span>
                </div>
            </div>
            ${teacher.remark ? `
                <hr style="margin:16px 0 20px 0;border-color:#e5e7eb;"/>
                <div class="mb-2 pb-3" style="font-size:1.08rem;">
                    <span style="color:#22304a;font-weight:600;"><i class="bi bi-chat-left-text me-1" style="color:#64748b;"></i>备注：</span>
                    <span class="text-muted" style="font-weight:500;">${teacher.remark}</span>
                </div>
            ` : '<div class=\"pb-2\"></div>'}
        </div>
    `;
    Swal.fire({
        title: '',
        html,
        width: 900,
        showCloseButton: true,
        showCancelButton: false,
        showConfirmButton: false,
        customClass: {popup: 'teacher-detail-modal'},
        didOpen: () => {
            const editBtn = document.getElementById('editTeacherDetailBtn');
            if (editBtn) {
                editBtn.onclick = function(e) {
                    e.stopPropagation();
                    Swal.close();
                    editTeacher(grade, teacher.id);
                };
            }
            const delBtn = document.getElementById('deleteTeacherDetailBtn');
            if (delBtn) {
                delBtn.onclick = function(e) {
                    e.stopPropagation();
                    Swal.close();
                    deleteTeacher(grade, teacher.name, teacher.id);
                };
            }
        }
    });
}

// 绑定新增教师按钮事件
const addTeacherBtn = document.getElementById('addTeacherBtn');
if (addTeacherBtn) {
    addTeacherBtn.addEventListener('click', addTeacher);
}

// 绑定导入教师按钮事件
const importTeacherBtn = document.getElementById('importTeacherBtn');
if (importTeacherBtn) {
    importTeacherBtn.addEventListener('click', function() {
        // 检查是否选择了年级
        const gradeBtn = document.querySelector('#teacherGradeBtnGroup .btn.active');
        const grade = gradeBtn ? gradeBtn.getAttribute('data-grade') : '';
        if (!grade) {
            Swal.fire('提示', '请先选择年级', 'info');
            return;
        }

        Swal.fire({
            title: '导入教师',
            html: `
                <div class="mb-3">
                    <label class="form-label">选择Excel文件</label>
                    <input type="file" class="form-control" id="teacherFileInput" accept=".xlsx,.xls" />
                </div>
                <div class="alert alert-info">
                    <small>
                        <strong>文件格式要求：</strong><br>
                        • 支持 .xlsx 和 .xls 格式<br>
                        • 第一行为表头：姓名、账号、密码、电话、任教证级、任教科目、备注<br>
                        • 任教科目可以为空或填写具体科目名称<br>
                        • 当前选择年级：<strong>${grade}</strong><br>
                        • 教师数据将导入到 <strong>${grade}</strong> 年级数据库中
                    </small>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: '开始导入',
            cancelButtonText: '取消',
            preConfirm: () => {
                const fileInput = document.getElementById('teacherFileInput');
                if (!fileInput.files.length) {
                    Swal.showValidationMessage('请选择要导入的文件');
                    return false;
                }
                return fileInput.files[0];
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // 这里可以添加实际的文件上传和导入逻辑
                // 传递年级参数到后端
                Swal.fire('提示', `导入功能开发中，将导入到${grade}年级数据库，敬请期待！`, 'info');
            }
        });
    });
}

// 预留：加载学生列表
function loadStudentList() {
    const cardList = document.getElementById('studentCardList');
    if (!cardList) return;
    cardList.innerHTML = '<div class="text-center py-5 text-muted"><span class="spinner-border spinner-border-sm"></span> 加载学生数据...</div>';
    // 新增：补全筛选器变量定义
    const searchInput = document.getElementById('studentSearchInput');
    const classFilter = document.getElementById('studentClassFilter');
    // 判断当前选中的年级
    const gradeBtn = document.querySelector('#studentGradeBtnGroup .btn.active');
    const grade = gradeBtn ? gradeBtn.getAttribute('data-grade') : '';
    // 请求学生数据
    let url = '/api/students';
    if (grade) url += `?grade=${encodeURIComponent(grade)}`;
    fetch(url)
        .then(res => res.json())
        .then(async data => {
            if (!data.success) {
                cardList.innerHTML = `<div class='text-center py-5 text-danger'>${data.error || '未找到学生数据'}</div>`;
                return;
            }
            let students = data.students || [];
            // 搜索和筛选功能
            function filterAndRender() {
                let filtered = students;
                const searchVal = searchInput ? searchInput.value.trim() : '';
                const classVal = classFilter ? classFilter.value : '';
                if (searchVal) {
                    filtered = filtered.filter(student =>
                        student.name.includes(searchVal) ||
                        student.username.includes(searchVal) ||
                        student.student_id.includes(searchVal) ||
                        (student.phone && student.phone.includes(searchVal)) ||
                        (student.class_name && student.class_name.includes(searchVal))
                    );
                }
                if (grade && classVal) {
                    filtered = filtered.filter(student => student.class_name === classVal);
                }
                renderStudentCards(filtered);
            }
            if (searchInput) searchInput.oninput = filterAndRender;
            if (classFilter) classFilter.onchange = filterAndRender;
            loadStudentFilters(grade, students);
            renderStudentCards(students);
        })
        .catch(err => {
            cardList.innerHTML = `<div class='text-center py-5 text-danger'>加载学生数据失败: ${err.message}</div>`;
        });
}

// ========================================
// 6. 学生管理模块
// ========================================

/**
 * 加载学生筛选选项
 * 根据年级加载对应的班级选项
 * @param {string} grade - 年级名称
 */
async function loadStudentFilters(grade) {
    const classFilter = document.getElementById('studentClassFilter');

    // 设置班级筛选选项
    if (classFilter) {
        if (!grade) {
            classFilter.innerHTML = '<option value="">全部班级</option>';
            classFilter.disabled = true;
        } else {
            classFilter.disabled = false;
            try {
                const data = await apiGet(`/api/classes?grade=${encodeURIComponent(grade)}`, false);
                if (data.success && data.classes) {
                    const classes = data.classes.map(c => c.class_name);
                    classFilter.innerHTML = `
                        <option value="">全部班级</option>
                        ${classes.map(c => `<option value="${c}">${c}</option>`).join('')}
                    `;
                }
            } catch (error) {
                console.error('加载班级筛选选项失败:', error);
            }
        }
    }
}

// 渲染学生卡片
async function renderStudentCards(students) {
    const cardList = document.getElementById('studentCardList');
    if (!cardList) return;
    if (!students.length) {
        cardList.innerHTML = '<div class="student-card-empty">暂无学生</div>';
        return;
    }
    cardList.innerHTML = '';
    const gradeBtn = document.querySelector('#studentGradeBtnGroup .btn.active');
    const grade = gradeBtn ? gradeBtn.getAttribute('data-grade') : '';
    for (const student of students) {
        const card = document.createElement('div');
        card.className = 'student-card position-relative';
        card.dataset.studentId = student.id;
        
        // 编辑/删除按钮
        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'student-card-actions';
        actionsDiv.innerHTML = `
            <button class="btn btn-outline-primary btn-sm me-1" onclick="event.stopPropagation(); editStudent('${student.grade}', ${student.id});"><i class="bi bi-pencil"></i> 编辑</button>
            <button class="btn btn-outline-danger btn-sm" onclick="event.stopPropagation(); deleteStudent('${student.grade}', '${student.name}', ${student.id});"><i class="bi bi-trash"></i> 删除</button>
        `;
        
        card.innerHTML = `
            <div class="student-card-header d-flex align-items-center justify-content-between">
                <div>
                    <span class="student-card-title">${student.name || ''}</span>
                    <span class="badge bg-secondary ms-2">${student.username || ''}</span>
                    <span class="badge bg-primary ms-2">学号: ${student.student_id || ''}</span>
                </div>
            </div>
            <div class="student-card-meta mt-1">
                <span class="badge bg-info">班级: ${student.class_name || '-'}</span>
                <span class="badge bg-warning text-dark ms-2">年级: ${student.grade || '-'}</span>
                <span class="badge bg-success ms-2">电话: ${student.phone || '-'}</span>
            </div>
            <div class="student-card-message mt-2">
                <span class="badge bg-primary">教师留言: ${(student.teacher_message || '无')}</span>
            </div>
            ${(student.feedback || '') ? `<div class="student-card-feedback mt-1"><small class="text-muted">反馈: ${student.feedback}</small></div>` : ''}
        `;
        
        card.appendChild(actionsDiv);
        
        // 鼠标悬停高亮和显示操作按钮
        card.onmouseenter = function() {
            card.classList.add('selected');
        };
        card.onmouseleave = function() {
            card.classList.remove('selected');
        };
        
        // 点击卡片弹窗显示详细信息
        card.onclick = function() {
            document.querySelectorAll('.student-card').forEach(c => c.classList.remove('selected'));
            card.classList.add('selected');
            showStudentDetailModal(student.grade, student);
        };
        
        cardList.appendChild(card);
    }
}

/**
 * 新增学生
 */
window.addStudent = function() {
    const gradeBtn = document.querySelector('#studentGradeBtnGroup .btn.active');
    const grade = gradeBtn ? gradeBtn.getAttribute('data-grade') : '';

    if (!grade) {
        Swal.fire('提示', '请先选择年级', 'info');
        return;
    }

    showAddStudentModal(grade);
};

/**
 * 显示新增学生模态框
 */
function showAddStudentModal(grade) {
    const modal = new bootstrap.Modal(document.getElementById('addStudentModal'));
    const form = document.getElementById('addStudentForm');

    // 重置表单
    form.reset();

    // 加载班级选项
    loadClassesForStudent(grade, form.class_name);

    // 绑定保存按钮事件
    const saveBtn = document.getElementById('saveAddStudentBtn');
    saveBtn.onclick = async function() {
        const formData = {
            name: form.name.value.trim(),
            student_id: form.student_id.value.trim(),
            password: form.password.value,
            class_name: form.class_name.value,
            gender: form.gender.value,
            phone: form.phone.value.trim(),
            teacher_message: form.teacher_message.value.trim(),
            feedback: form.feedback.value.trim()
        };

        // 验证必填字段
        if (!formData.name) {
            Swal.fire('提示', '姓名不能为空', 'warning');
            return;
        }
        if (!formData.student_id) {
            Swal.fire('提示', '学号不能为空', 'warning');
            return;
        }
        if (!formData.password) {
            Swal.fire('提示', '密码不能为空', 'warning');
            return;
        }
        if (!formData.class_name) {
            Swal.fire('提示', '请选择班级', 'warning');
            return;
        }

        // 显示保存中状态
        const originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>保存中...';
        saveBtn.disabled = true;

        try {
            const response = await fetch('/api/students', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({grade, ...formData})
            });
            const data = await response.json();

            if (data.success) {
                showSuccessToast('学生添加成功');
                modal.hide();
                loadStudentList();
            } else {
                throw new Error(data.error || '添加失败');
            }
        } catch (error) {
            console.error('添加学生失败:', error);
            showErrorToast(`添加失败: ${error.message}`);
        } finally {
            // 恢复按钮状态
            saveBtn.innerHTML = originalText;
            saveBtn.disabled = false;
        }
    };

    modal.show();
}

/**
 * 为学生表单加载班级选项
 */
async function loadClassesForStudent(grade, selectElement) {
    try {
        const response = await fetch(`/api/classes?grade=${encodeURIComponent(grade)}`);
        const data = await response.json();

        // 清空现有选项（保留默认选项）
        selectElement.innerHTML = '<option value="">请选择班级</option>';

        if (data.success && data.classes) {
            data.classes.forEach(cls => {
                const option = document.createElement('option');
                option.value = cls.class_name;
                option.textContent = cls.class_name;
                selectElement.appendChild(option);
            });
        }
    } catch (error) {
        console.error('加载班级列表失败:', error);
    }
}

// 编辑学生
window.editStudent = function(grade, studentId) {
    fetch(`/api/students/${studentId}?grade=${encodeURIComponent(grade)}`)
        .then(res => res.json())
        .then(data => {
            if (!data.success) {
                Swal.fire('错误', data.error || '未找到学生信息', 'error');
                return;
            }
            showEditStudentModal(grade, data.student);
        })
        .catch(err => {
            Swal.fire('错误', '网络错误，获取学生信息失败', 'error');
        });
};

/**
 * 显示编辑学生模态框
 */
function showEditStudentModal(grade, student) {
    const modal = new bootstrap.Modal(document.getElementById('editStudentModal'));
    const form = document.getElementById('editStudentForm');

    // 填充表单数据
    form.name.value = student.name || '';
    form.student_id.value = student.student_id || '';
    form.password.value = '';
    form.gender.value = student.gender || '';
    form.phone.value = student.phone || '';
    form.teacher_message.value = student.teacher_message || '';
    form.feedback.value = student.feedback || '';

    // 加载班级选项并设置选中状态
    loadClassesForEditStudent(grade, form.class_name, student.class_name);

    // 绑定保存按钮事件
    const saveBtn = document.getElementById('saveEditStudentBtn');
    saveBtn.onclick = async function() {
        const formData = {
            name: form.name.value.trim(),
            student_id: form.student_id.value.trim(),
            password: form.password.value,
            class_name: form.class_name.value,
            gender: form.gender.value,
            phone: form.phone.value.trim(),
            teacher_message: form.teacher_message.value.trim(),
            feedback: form.feedback.value.trim()
        };

        // 验证必填字段
        if (!formData.name) {
            Swal.fire('提示', '姓名不能为空', 'warning');
            return;
        }
        if (!formData.student_id) {
            Swal.fire('提示', '学号不能为空', 'warning');
            return;
        }
        if (!formData.class_name) {
            Swal.fire('提示', '请选择班级', 'warning');
            return;
        }

        // 显示保存中状态
        const originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>保存中...';
        saveBtn.disabled = true;

        try {
            const response = await fetch(`/api/students/${student.id}`, {
                method: 'PUT',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({grade, ...formData})
            });
            const data = await response.json();

            if (data.success) {
                showSuccessToast('学生信息已保存');
                modal.hide();
                loadStudentList();
            } else {
                throw new Error(data.error || '保存失败');
            }
        } catch (error) {
            console.error('保存学生信息失败:', error);
            showErrorToast(`保存失败: ${error.message}`);
        } finally {
            // 恢复按钮状态
            saveBtn.innerHTML = originalText;
            saveBtn.disabled = false;
        }
    };

    modal.show();
}

/**
 * 为编辑学生表单加载班级选项
 */
async function loadClassesForEditStudent(grade, selectElement, selectedClass) {
    try {
        const response = await fetch(`/api/classes?grade=${encodeURIComponent(grade)}`);
        const data = await response.json();

        // 清空现有选项（保留默认选项）
        selectElement.innerHTML = '<option value="">请选择班级</option>';

        if (data.success && data.classes) {
            data.classes.forEach(cls => {
                const option = document.createElement('option');
                option.value = cls.class_name;
                option.textContent = cls.class_name;
                if (cls.class_name === selectedClass) {
                    option.selected = true;
                }
                selectElement.appendChild(option);
            });
        }
    } catch (error) {
        console.error('加载班级列表失败:', error);
    }
}

// 删除学生
window.deleteStudent = function(grade, studentName, studentId) {
    Swal.fire({
        title: '确认删除',
        text: `确定要删除学生：${studentName} 吗？`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: '删除',
        cancelButtonText: '取消'
    }).then(result => {
        if (result.isConfirmed) {
            fetch(`/api/students/${studentId}?grade=${encodeURIComponent(grade)}`, {
                method: 'DELETE'
            })
            .then(res => res.json())
            .then(data => {
                if (data.success) {
                    Swal.fire('成功', '学生已删除', 'success');
                    loadStudentList();
                } else {
                    Swal.fire('错误', data.error || '删除失败', 'error');
                }
            });
        }
    });
}

// 学生详细信息弹窗
async function showStudentDetailModal(grade, student) {
    let html = `
        <div class="student-detail-modal" style="max-width:100%;overflow-x:hidden;font-size:1.13rem;line-height:2.1;background:#f8f9fb;border-radius:18px;box-shadow:0 8px 32px 0 rgba(60,60,90,0.18);padding:44px 60px 40px 60px;">
            <div class="d-flex justify-content-between align-items-center mb-4" style="min-height:48px;">
                <div class="d-flex align-items-center">
                    <h2 class="fw-bold mb-0 me-3" style="font-size:2.1rem;color:#2d3a4a;"><i class="bi bi-person me-2 text-primary"></i>${student.name}</h2>
                </div>
                <div>
                    <button class="btn btn-outline-primary btn-sm me-2" id="editStudentDetailBtn" style="font-size:1.1rem;padding:6px 20px;border-radius:8px;"><i class="bi bi-pencil"></i> 编辑</button>
                    <button class="btn btn-outline-danger btn-sm" id="deleteStudentDetailBtn" style="font-size:1.1rem;padding:6px 20px;border-radius:8px;"><i class="bi bi-trash"></i> 删除</button>
                </div>
            </div>
            <div class="row mb-4 g-3 flex-wrap" style="font-size:1.08rem;">
                <div class="col-md-6 mb-2" style="word-break:break-all;">
                    <span style="color:#22304a;font-weight:600;"><i class="bi bi-person-circle me-1" style="color:#3b82f6;"></i>账号：</span><span style="color:#4b5563;font-weight:500;">${student.username || '-'}</span>
                </div>
                <div class="col-md-6 mb-2" style="word-break:break-all;">
                    <span style="color:#22304a;font-weight:600;"><i class="bi bi-card-text me-1" style="color:#22c55e;"></i>学号：</span><span style="color:#4b5563;font-weight:500;">${student.student_id || '-'}</span>
                </div>
                <div class="col-md-6 mb-2">
                    <span style="color:#22304a;font-weight:600;"><i class="bi bi-people me-1" style="color:#f59e42;"></i>班级：</span><span style="color:#4b5563;font-weight:500;">${student.class_name || '-'}</span>
                </div>
                <div class="col-md-6 mb-2">
                    <span style="color:#22304a;font-weight:600;"><i class="bi bi-building me-1" style="color:#64748b;"></i>年级：</span><span style="color:#4b5563;font-weight:500;">${student.grade || '-'}</span>
                </div>
                <div class="col-md-6 mb-2">
                    <span style="color:#22304a;font-weight:600;"><i class="bi bi-telephone me-1" style="color:#06b6d4;"></i>电话：</span><span style="color:#4b5563;font-weight:500;">${student.phone || '-'}</span>
                </div>
                <div class="col-md-6 mb-2" style="word-break:break-all;">
                    <span style="color:#22304a;font-weight:600;"><i class="bi bi-key me-1" style="color:#64748b;"></i>密码：</span><span style="color:#4b5563;font-weight:500;">${student.password ? student.password : '<span class=\"text-muted\">（无）</span>'}</span>
                </div>
            </div>
            <hr style="margin:16px 0 20px 0;border-color:#e5e7eb;"/>
            <div class="row mb-4 g-3 flex-wrap" style="font-size:1.08rem;">
                <div class="col-12 mb-2">
                    <span style="color:#22304a;font-weight:600;"><i class="bi bi-chat-left-text me-1" style="color:#2563eb;"></i>教师留言：</span>
                    <span style="color:#4b5563;font-weight:500;">${student.teacher_message || '<span class="text-muted">无</span>'}</span>
                </div>
                ${student.feedback ? `
                <div class="col-12 mb-2">
                    <span style="color:#22304a;font-weight:600;"><i class="bi bi-chat-dots me-1" style="color:#ef4444;"></i>反馈：</span>
                    <span style="color:#4b5563;font-weight:500;">${student.feedback}</span>
                </div>
                ` : ''}
            </div>
        </div>
    `;
    Swal.fire({
        title: '',
        html,
        width: 900,
        showCloseButton: true,
        showCancelButton: false,
        showConfirmButton: false,
        customClass: {popup: 'student-detail-modal'},
        didOpen: () => {
            const editBtn = document.getElementById('editStudentDetailBtn');
            if (editBtn) {
                editBtn.onclick = function(e) {
                    e.stopPropagation();
                    Swal.close();
                    editStudent(grade, student.id);
                };
            }
            const delBtn = document.getElementById('deleteStudentDetailBtn');
            if (delBtn) {
                delBtn.onclick = function(e) {
                    e.stopPropagation();
                    Swal.close();
                    deleteStudent(grade, student.name, student.id);
                };
            }
        }
    });
}

// 绑定新增学生按钮事件
const addStudentBtn = document.getElementById('addStudentBtn');
if (addStudentBtn) {
    addStudentBtn.addEventListener('click', addStudent);
}

// 绑定导入学生按钮事件
const importStudentBtn = document.getElementById('importStudentBtn');
if (importStudentBtn) {
    importStudentBtn.addEventListener('click', function() {
        // 检查是否选择了年级
        const gradeBtn = document.querySelector('#studentGradeBtnGroup .btn.active');
        const grade = gradeBtn ? gradeBtn.getAttribute('data-grade') : '';
        if (!grade) {
            Swal.fire('提示', '请先选择年级', 'info');
            return;
        }

        Swal.fire({
            title: '导入学生',
            html: `
                <div class="mb-3">
                    <label class="form-label">选择Excel文件</label>
                    <input type="file" class="form-control" id="studentFileInput" accept=".xlsx,.xls" />
                </div>
                <div class="alert alert-info">
                    <small>
                        <strong>文件格式要求：</strong><br>
                        • 支持 .xlsx 和 .xls 格式<br>
                        • 第一行为表头：姓名、学号、密码、班级、性别、电话、教师留言、反馈<br>
                        • 班级名称必须是 <strong>${grade}</strong> 年级中已存在的班级<br>
                        • 性别可填写"男"或"女"，也可以为空<br>
                        • 当前选择年级：<strong>${grade}</strong><br>
                        • 学生数据将导入到 <strong>${grade}</strong> 年级数据库中
                    </small>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: '开始导入',
            cancelButtonText: '取消',
            preConfirm: () => {
                const fileInput = document.getElementById('studentFileInput');
                if (!fileInput.files.length) {
                    Swal.showValidationMessage('请选择要导入的文件');
                    return false;
                }
                return fileInput.files[0];
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // 这里可以添加实际的文件上传和导入逻辑
                // 传递年级参数到后端
                Swal.fire('提示', `导入功能开发中，将导入到${grade}年级数据库，敬请期待！`, 'info');
            }
        });
    });
}

// 获取并渲染年级-考试平均分和图表
async function loadGradeExamAverage(grade, exam) {
    if (!grade || !exam) return;
    try {
        const res = await fetch(`/api/grade-exam-average?grade=${encodeURIComponent(grade)}&exam=${encodeURIComponent(exam)}`);
        const data = await res.json();
        if (!data.success) throw new Error(data.error || '获取年级平均分失败');

        // 渲染总分平均分
        const totalAvgElement = document.getElementById('grade-total-avg');
        if (totalAvgElement) {
            totalAvgElement.textContent = data.avg_data['总分'] !== undefined ? data.avg_data['总分'].toFixed(1) : '--';
        }

        // 渲染各科平均分和统计人数
        renderGradeAverages(data.avg_data);

        // 使用createChartCard函数渲染图表
        const gradeVisualizationRow = document.querySelector('.grade-visualization-row');
        if (gradeVisualizationRow && data.charts) {
            gradeVisualizationRow.innerHTML = '';
            
            // 创建雷达图卡片
            if (data.charts.radar) {
                const radarCard = createChartCard(data.charts.radar, '年级雷达图', 'radar', { 
                    examName: exam, 
                    className: `${grade}年级`, 
                    studentName: '年级平均分', 
                    chartType: '雷达图', 
                    useServerDownload: true 
                });
                gradeVisualizationRow.appendChild(radarCard);
            }
            
            // 创建柱状图卡片
            if (data.charts.bar) {
                const barCard = createChartCard(data.charts.bar, '年级柱状图', 'bar', { 
                    examName: exam, 
                    className: `${grade}年级`, 
                    studentName: '年级平均分', 
                    chartType: '柱状图', 
                    useServerDownload: true 
                });
                gradeVisualizationRow.appendChild(barCard);
            }
            
            // 创建表格图卡片
            if (data.charts.table) {
                const tableCard = createChartCard(data.charts.table, '年级成绩表', 'table', { 
                    examName: exam, 
                    className: `${grade}年级`, 
                    studentName: '年级平均分', 
                    chartType: '表格图', 
                    useServerDownload: true 
                });
                gradeVisualizationRow.appendChild(tableCard);
            }
        }
    } catch (e) {
        console.error('加载年级平均分失败:', e);
        const totalAvgElement = document.getElementById('grade-total-avg');
        if (totalAvgElement) {
            totalAvgElement.textContent = '--';
        }
        const subjectRowElement = document.getElementById('grade-subject-avg-row');
        if (subjectRowElement) {
            subjectRowElement.innerHTML = '';
        }
        // 显示错误信息
        const gradeVisualizationRow = document.querySelector('.grade-visualization-row');
        if (gradeVisualizationRow) {
            gradeVisualizationRow.innerHTML = `
                <div class="col-12 text-center py-4">
                    <i class="bi bi-exclamation-triangle text-warning fs-3"></i>
                    <p class="mt-2 text-muted">加载年级图表失败</p>
                </div>
            `;
        }
    }
    animateSection('gradeDetailCard');
}



// ========================================
// 7. 图表和数据可视化模块
// ========================================

/**
 * 渲染班级类型分布饼图
 * @param {Object} typeCount - 班级类型统计数据 {类型名: 数量}
 */
function renderClassTypePieChart(typeCount) {
    const pieDiv = document.getElementById('classTypePieChart');
    if (!pieDiv) {
        console.warn('classTypePieChart element not found');
        return;
    }

    if (!window.Chart) {
        console.warn('Chart.js not loaded');
        return;
    }

    // 动态调整canvas尺寸以优化外部框长度
    const container = pieDiv.parentElement;
    const containerWidth = container ? container.clientWidth : 220;
    const optimalSize = Math.min(containerWidth - 40, 200); // 减去padding，最大200px

    // 设置canvas尺寸
    pieDiv.style.width = optimalSize + 'px';
    pieDiv.style.height = optimalSize + 'px';

    const ctx = pieDiv.getContext('2d');

    // 销毁之前的图表实例
    if (window._classTypePieChart) {
        window._classTypePieChart.destroy();
    }

    // 如果没有数据，显示空状态
    if (Object.keys(typeCount).length === 0) {
        pieDiv.style.display = 'none';
        const emptyDiv = container.querySelector('.empty-chart-message') || document.createElement('div');
        emptyDiv.className = 'empty-chart-message text-muted text-center py-3';
        emptyDiv.textContent = '暂无数据';
        if (!container.querySelector('.empty-chart-message')) {
            container.appendChild(emptyDiv);
        }
        return;
    } else {
        // 有数据时显示canvas，隐藏空状态消息
        pieDiv.style.display = 'block';
        const emptyDiv = container.querySelector('.empty-chart-message');
        if (emptyDiv) {
            emptyDiv.remove();
        }
    }

    // 创建新的饼图
    window._classTypePieChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: Object.keys(typeCount),
            datasets: [{
                data: Object.values(typeCount),
                backgroundColor: ['#527aaf', '#f6c23e', '#e74a3b', '#36b9cc', '#858796'],
                borderWidth: 2,
                borderColor: '#ffffff',
                // 调整饼图扇形之间的间距
                spacing: 2
            }]
        },
        options: {
            // 设置图表的内边距，控制外部框的大小
            layout: {
                padding: {
                    top: 5,
                    bottom: 30, // 为底部图例留出空间
                    left: 5,
                    right: 5
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'bottom',
                    // 优化图例样式，控制外部框长度
                    labels: {
                        padding: 8, // 减少图例间距
                        usePointStyle: true,
                        pointStyle: 'circle',
                        font: {
                            size: 11 // 稍微减小字体
                        },
                        boxWidth: 12, // 控制图例色块大小
                        boxHeight: 12,
                        // 控制图例文字的长度和布局
                        generateLabels: function(chart) {
                            const data = chart.data;
                            if (data.labels.length && data.datasets.length) {
                                return data.labels.map((label, i) => {
                                    const dataset = data.datasets[0];
                                    const value = dataset.data[i];
                                    const total = dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((value / total) * 100).toFixed(1);

                                    // 智能缩短标签，避免外部框过长
                                    let shortLabel = label;
                                    if (label.length > 6) {
                                        shortLabel = label.substring(0, 6) + '...';
                                    }

                                    return {
                                        text: `${shortLabel} ${percentage}%`,
                                        fillStyle: dataset.backgroundColor[i],
                                        strokeStyle: dataset.borderColor || '#fff',
                                        lineWidth: dataset.borderWidth || 1,
                                        pointStyle: 'circle',
                                        hidden: false,
                                        index: i
                                    };
                                });
                            }
                            return [];
                        }
                    },
                    // 控制图例的最大宽度
                    maxWidth: optimalSize - 20,
                    // 图例换行设置
                    align: 'center'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            },
            responsive: true,
            maintainAspectRatio: true,
            // 设置图表的宽高比，控制外部框比例
            aspectRatio: 1.1, // 稍微压缩高度
            // 优化饼图的显示区域
            elements: {
                arc: {
                    // 设置饼图的边框对齐方式
                    borderAlign: 'center',
                    // 控制饼图扇形的边框宽度
                    borderWidth: 1
                }
            },
            // 设置图表的动画，让变化更平滑
            animation: {
                animateRotate: true,
                animateScale: false,
                duration: 800
            },
            // 控制图表在容器中的位置
            devicePixelRatio: window.devicePixelRatio || 1
        }
    });
}


// ========================================
// 8. 动画和UI效果模块
// ========================================

/**
 * 为指定元素添加淡入向上动画效果
 * 用于渲染表格、统计区块时增加视觉效果
 * @param {string} sectionId - 元素ID
 */
function animateSection(sectionId) {
    const el = document.getElementById(sectionId);
    if (el) {
        el.classList.add('animate__animated', 'animate__fadeInUp');
        // 900ms后移除动画类，避免重复动画时的冲突
        setTimeout(() => el.classList.remove('animate__fadeInUp'), 900);
    }
}



async function renderClassCards(list) {
    const cardList = document.getElementById('classCardList');
    if (!list.length) {
        cardList.innerHTML = '<div class="class-card-empty">暂无班级</div>';
        return;
    }
    // 先显示 loading
    cardList.innerHTML = '<div class="text-center py-4"><div class="spinner-border text-primary"></div><p class="mt-2 text-muted">正在加载班级信息...</p></div>';
    try {
        // 渲染前清空 loading
        cardList.innerHTML = '';
        const gradeBtn = document.querySelector('#classGradeBtnGroup .btn.active');
        const grade = gradeBtn ? gradeBtn.getAttribute('data-grade') : '';
        for (const cls of list) {
            // 异步获取班主任和任课老师信息
            const [teacherRes, leaveRes] = await Promise.all([
                fetch(`/api/class-teachers?grade=${encodeURIComponent(grade)}&class_name=${encodeURIComponent(cls.class_name)}`).then(r=>r.json()),
                fetch(`/api/leaves?grade=${encodeURIComponent(grade)}&class_name=${encodeURIComponent(cls.class_name)}`).then(r=>r.json())
            ]);
            const card = document.createElement('div');
            card.className = 'class-card';
            card.dataset.classId = cls.id;
            // 操作按钮
            const actions = `
                <div class="class-card-actions">
                    <button class="btn btn-outline-primary btn-sm me-1" onclick="event.stopPropagation(); editClass('${grade}', ${cls.id});"><i class="bi bi-pencil"></i> 编辑</button>
                    <button class="btn btn-outline-danger btn-sm" onclick="event.stopPropagation(); deleteClass('${grade}', '${cls.class_name}', ${cls.id});"><i class="bi bi-trash"></i> 删除</button>
                </div>
            `;
            // 班主任
            let headTeacher = teacherRes && teacherRes.head_teacher && teacherRes.head_teacher.name ? teacherRes.head_teacher.name : (cls.head_teacher || '-');
            // 任课老师
            let subjectTeachers = (teacherRes && teacherRes.subject_teachers) ? teacherRes.subject_teachers : [];
            card.innerHTML = `
                <div class="class-card-header d-flex align-items-center justify-content-between">
                    <div>
                        <span class="class-card-title">${cls.class_name}</span>
                        <span class="badge bg-primary ms-2">${cls.class_type || '普通班级'}</span>
                        <span class="badge bg-secondary ms-2">人数: ${cls.class_size || '-'}</span>
                    </div>
                    <div class="class-card-actions" style="opacity:0;transition:opacity 0.2s;">${actions}</div>
                </div>
                <div class="class-card-meta mt-1">
                    <span class="badge bg-success">班主任: ${headTeacher}</span>
                    <span class="badge bg-warning text-dark ms-2">请假: <span class="class-leave-val">${leaveRes.success && leaveRes.data ? leaveRes.data.length : 0}</span></span>
                </div>
                <div class="class-card-teachers mt-2">
                    ${subjectTeachers.length ? subjectTeachers.map(t => `<span class='badge bg-info me-1'>${t.course_name}: ${t.name}</span>`).join('') : '<span class="text-muted">暂无任课老师</span>'}
                </div>
            `;
            card.onmouseenter = function() {
                card.querySelector('.class-card-actions').style.opacity = 1;
            };
            card.onmouseleave = function() {
                card.querySelector('.class-card-actions').style.opacity = 0;
            };
            card.onclick = function() {
                document.querySelectorAll('.class-card').forEach(c => c.classList.remove('selected'));
                card.classList.add('selected');
                showClassDetailModal(grade, cls);
            };
            cardList.appendChild(card);
        }

        // 渲染完成后，应用当前的筛选条件
        setTimeout(() => {
            filterAndRenderClasses();
        }, 100);

    } catch (error) {
        console.error('渲染班级卡片失败:', error);
        cardList.innerHTML = '<div class="class-card-empty text-danger">加载班级信息失败，请刷新页面重试</div>';
    }
}

// 旧的下载函数已删除，使用新的downloadImageToServer函数



// 班级管理面板年级切换逻辑
function loadClassGradeList() {
    const gradeBtnGroup = document.getElementById('classGradeBtnGroup');
    // 获取年级列表
    const adminGrades = (sessionStorage.getItem('grade') || '').split(',').filter(Boolean);
    if (!gradeBtnGroup) return;
    gradeBtnGroup.innerHTML = adminGrades.map((g,i) =>
        `<button type="button" class="btn btn-outline-primary${i===0?' active':''}" data-grade="${g}">${g}</button>`
    ).join('');
    // 年级按钮事件
    Array.from(gradeBtnGroup.children).forEach((btn, i) => {
        btn.onclick = function() {
            Array.from(gradeBtnGroup.children).forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            loadClassList();
        };
    });
    // 默认选中第一个年级
    if (adminGrades.length) {
        Array.from(gradeBtnGroup.children)[0].classList.add('active');
        loadClassList();
    }
}

// 教师管理面板年级切换逻辑
function loadTeacherGradeList() {
    const gradeBtnGroup = document.getElementById('teacherGradeBtnGroup');
    const adminGrades = (sessionStorage.getItem('grade') || '').split(',').filter(Boolean);
    if (!gradeBtnGroup) return;
    // 渲染年级按钮组
    gradeBtnGroup.innerHTML = adminGrades.map((g,i) =>
        `<button type="button" class="btn btn-outline-primary${i===0?' active':''}" data-grade="${g}">${g}</button>`
    ).join('');
    // 绑定点击事件
    Array.from(gradeBtnGroup.children).forEach((btn, i) => {
        btn.onclick = function() {
            Array.from(gradeBtnGroup.children).forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            // 切换年级时刷新教师列表
            loadTeacherList();
        };
    });
    // 默认选中第一个年级并加载教师列表
    if (adminGrades.length) {
        Array.from(gradeBtnGroup.children)[0].classList.add('active');
        loadTeacherList();
    }
}

// 页面初始化时分别加载年级按钮组
window.addEventListener('DOMContentLoaded', function() {
    function afterGradeReady() {
        loadClassGradeList();
        // 如果当前Tab是教师管理，主动刷新教师按钮组
        if (document.getElementById('teacherTabBtn').classList.contains('active')) {
            loadTeacherGradeList();
        }
        // 如果当前Tab是学生管理，主动刷新学生按钮组
        if (document.getElementById('studentTabBtn').classList.contains('active')) {
            loadStudentGradeList();
        }
        // 如果当前Tab是考试管理，主动刷新考试按钮组
        if (document.getElementById('examTabBtn').classList.contains('active')) {
            loadExamGradeList();
        }
    }
    if (!sessionStorage.getItem('grade')) {
        fetch('/api/exams')
            .then(res => res.json())
            .then(data => {
                let grades = Array.isArray(data) ? data : (data.grades || []);
                if (grades.length > 0) {
                    sessionStorage.setItem('grade', grades.join(','));
                }
                afterGradeReady();
            });
    } else {
        afterGradeReady();
    }
});

// 学生管理面板年级切换逻辑
function loadStudentGradeList() {
    const gradeBtnGroup = document.getElementById('studentGradeBtnGroup');
    const adminGrades = (sessionStorage.getItem('grade') || '').split(',').filter(Boolean);
    if (!gradeBtnGroup) return;
    // 渲染年级按钮组
    gradeBtnGroup.innerHTML = adminGrades.map((g,i) =>
        `<button type="button" class="btn btn-outline-primary${i===0?' active':''}" data-grade="${g}">${g}</button>`
    ).join('');
    // 绑定点击事件
    Array.from(gradeBtnGroup.children).forEach((btn, i) => {
        btn.onclick = function() {
            Array.from(gradeBtnGroup.children).forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            // 切换年级时刷新学生列表
            loadStudentList();
        };
    });
    // 默认选中第一个年级并加载学生列表
    if (adminGrades.length) {
        Array.from(gradeBtnGroup.children)[0].classList.add('active');
        loadStudentList();
    }
}

// 考试管理面板年级切换逻辑
function loadExamGradeList() {
    const gradeBtnGroup = document.getElementById('examGradeBtnGroup');
    const adminGrades = (sessionStorage.getItem('grade') || '').split(',').filter(Boolean);
    if (!gradeBtnGroup) return;

    // 渲染年级按钮组
    gradeBtnGroup.innerHTML = adminGrades.map((g,i) =>
        `<button type="button" class="btn btn-outline-primary${i===0?' active':''}" data-grade="${g}">${g}</button>`
    ).join('');

    // 绑定点击事件
    Array.from(gradeBtnGroup.children).forEach((btn, i) => {
        btn.onclick = function() {
            Array.from(gradeBtnGroup.children).forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            // 切换年级时刷新考试列表和重置考试详情
            loadExamListForManagement();
            hideExamDetail();
        };
    });

    // 默认选中第一个年级并加载考试列表
    if (adminGrades.length) {
        Array.from(gradeBtnGroup.children)[0].classList.add('active');
        loadExamListForManagement();
    }
}

// 加载考试列表（考试管理专用）
function loadExamListForManagement() {
    const gradeBtn = document.querySelector('#examGradeBtnGroup .btn.active');
    const grade = gradeBtn ? gradeBtn.getAttribute('data-grade') : '';
    const examSelect = document.getElementById('examFilterSelect');

    if (!grade || !examSelect) return;

    // 清空考试选择器
    examSelect.innerHTML = '<option value="">请选择考试</option>';

    // 从后端获取考试列表
    fetch(`/api/exam-management?grade=${encodeURIComponent(grade)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.exams) {
                // 填充考试选择器
                data.exams.forEach(exam => {
                    const option = document.createElement('option');
                    option.value = exam.id;
                    option.textContent = `${exam.name} (${exam.date})`;
                    examSelect.appendChild(option);
                });
            } else {
                console.warn('获取考试列表失败:', data.error || '未知错误');
                // 使用模拟数据作为备选
                const mockExams = [
                    { id: 1, name: '第一次月考', date: '2024-03-15' },
                    { id: 2, name: '期中考试', date: '2024-04-20' },
                    { id: 3, name: '第二次月考', date: '2024-05-10' }
                ];

                mockExams.forEach(exam => {
                    const option = document.createElement('option');
                    option.value = exam.id;
                    option.textContent = `${exam.name} (${exam.date})`;
                    examSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('获取考试列表失败:', error);
            // 使用模拟数据作为备选
            const mockExams = [
                { id: 1, name: '第一次月考', date: '2024-03-15' },
                { id: 2, name: '期中考试', date: '2024-04-20' },
                { id: 3, name: '第二次月考', date: '2024-05-10' }
            ];

            mockExams.forEach(exam => {
                const option = document.createElement('option');
                option.value = exam.id;
                option.textContent = `${exam.name} (${exam.date})`;
                examSelect.appendChild(option);
            });
        });

    // 绑定考试选择事件
    examSelect.onchange = function() {
        if (this.value) {
            showExamDetail(this.value, this.options[this.selectedIndex].text);
        } else {
            hideExamDetail();
        }
    };
}

// 显示考试详情
function showExamDetail(examId, examText) {
    const examDetailCard = document.getElementById('examDetailCard');
    const examDetailName = document.getElementById('examDetailName');
    const examDetailInfo = document.getElementById('examDetailInfo');

    if (!examDetailCard || !examDetailName || !examDetailInfo) return;

    // 显示考试详情卡片
    examDetailCard.style.display = 'block';
    examDetailName.textContent = examText.split(' (')[0];
    examDetailInfo.textContent = `考试日期：${examText.split(' (')[1]?.replace(')', '') || ''}`;

    // 加载考试统计数据（模拟数据）
    loadExamStatistics(examId);

    // 加载年级成绩数据
    loadExamGradeData(examId);
}

// 隐藏考试详情
function hideExamDetail() {
    const examDetailCard = document.getElementById('examDetailCard');
    if (examDetailCard) {
        examDetailCard.style.display = 'none';
    }
}

// 加载考试统计数据
function loadExamStatistics(examId) {
    // 模拟统计数据
    const mockStats = {
        studentCount: 450,
        classCount: 12,
        totalAvg: 485.6
    };

    document.getElementById('examStudentCount').textContent = mockStats.studentCount;
    document.getElementById('examClassCount').textContent = mockStats.classCount;
    document.getElementById('examTotalAvg').textContent = mockStats.totalAvg.toFixed(1);
}

// 加载年级成绩数据
function loadExamGradeData(examId) {
    // 这里应该加载实际的年级成绩数据
    // 包括年级总分平均分、各科平均分、图表数据等

    // 模拟数据
    document.getElementById('exam-grade-total-avg').textContent = '485.6';

    // 模拟年级成绩表数据
    const mockGradeData = [
        { className: '1班', teacher: '张老师', count: 38, avg: 492.5, max: 650, min: 320 },
        { className: '2班', teacher: '李老师', count: 37, avg: 488.2, max: 635, min: 315 },
        { className: '3班', teacher: '王老师', count: 39, avg: 481.7, max: 628, min: 298 }
    ];

    const tableBody = document.getElementById('examGradeTableBody');
    if (tableBody) {
        tableBody.innerHTML = mockGradeData.map(row => `
            <tr>
                <td>${row.className}</td>
                <td>${row.teacher}</td>
                <td>${row.count}</td>
                <td>${row.avg}</td>
                <td>${row.max}</td>
                <td>${row.min}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary">查看详情</button>
                </td>
            </tr>
        `).join('');
    }
}

// 工具函数：创建图表卡片（与教师分析页面保持一致）
function createChartCard(chartUrl, title, type, options = {}) {
    // options: { examName, className, studentName, chartType, useServerDownload }
    const card = document.createElement('div');
    card.className = 'col-md-6 col-lg-4';
    if (typeof chartUrl !== 'string' || !chartUrl) {
        card.innerHTML = `
            <div class="card h-100 shadow-sm">
                <div class="card-header bg-light">
                    <h6 class="card-title mb-0">${title}</h6>
                </div>
                <div class="card-body text-center text-muted">
                    <i class="bi bi-exclamation-triangle fs-2"></i>
                    <div>暂无图表</div>
                </div>
            </div>
        `;
        return card;
    }
    const imageUrl = chartUrl.startsWith('/images/') ? chartUrl : `/images/${chartUrl}`;
    card.innerHTML = `
        <div class="card h-100 shadow-sm">
            <div class="card-header bg-light">
                <h6 class="card-title mb-0">${title}</h6>
            </div>
            <div class="card-body">
                <img src="${imageUrl}" class="img-fluid chart-image" alt="${title}"
                     style="cursor: pointer;" onclick="showEnlargedImage('${imageUrl}', '${title}')"
                     onerror="this.onerror=null; console.error('Failed to load image:', this.src);">
            </div>
            <div class="card-footer bg-light text-center">
                ${options.useServerDownload ? `
                <button class="btn btn-outline-primary btn-sm" onclick="downloadImageToServer('${imageUrl}', '${options.examName}', '${options.className}', '${options.studentName}', '${options.chartType}')">
                    <i class="bi bi-download"></i> 下载
                </button>
                ` : `
                <button class="btn btn-sm btn-outline-primary" onclick="downloadImage('${imageUrl}', '${title}')">
                    <i class="bi bi-download"></i> 下载
                </button>
                `}
            </div>
        </div>
    `;
    return card;
}

// 下载图片到服务器（与教师分析页面保持一致）
function downloadImageToServer(imageUrl, examName, className, studentName, chartType) {
    // 从imageUrl中提取图片路径
    const imagePath = imageUrl.replace('/images/', '');
    const grade = sessionStorage.getItem('grade');
    
    fetch('/download_image', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            image_path: imagePath,
            grade: grade,
            exam: examName,
            class_name: className,
            student_name: studentName || ''
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: '图片已下载',
                text: `图片已保存至：${data.filepath}`
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: '下载失败',
                text: data.error || '图片下载失败'
            });
        }
    })
    .catch(() => {
        Swal.fire({
            icon: 'error',
            title: '下载失败',
            text: '网络错误，图片下载失败'
        });
    });
}

// 从浏览器下载图片（与教师分析页面保持一致）
function downloadImage(url, title) {
    console.log('Downloading image:', { url, title });
    const link = document.createElement('a');
    link.href = url;
    link.download = `${title}_${new Date().getTime()}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// ========================================
// 科目选择器相关函数（单选版本）
// ========================================

/**
 * 切换科目选择器的展开/收起状态
 */
window.toggleSubjectSelector = function() {
    const content = document.getElementById('subjectSelectorContent');
    const icon = document.getElementById('subjectSelectorIcon');
    const selector = document.getElementById('subjectSelector');

    if (content.style.display === 'none') {
        content.style.display = 'block';
        icon.className = 'bi bi-chevron-up';
        selector.classList.add('expanded');
    } else {
        content.style.display = 'none';
        icon.className = 'bi bi-chevron-down';
        selector.classList.remove('expanded');
    }
};

/**
 * 更新科目选择器头部显示的文本
 */
function updateSubjectSelectorHeader() {
    const selectedSubject = getSelectedSubject();
    const headerText = document.querySelector('#subjectSelector .subject-selector-header span');

    if (!headerText) return;

    if (!selectedSubject) {
        headerText.textContent = '请选择科目';
    } else {
        headerText.textContent = selectedSubject;
    }
}

/**
 * 获取选中的科目
 */
function getSelectedSubject() {
    const checkedRadio = document.querySelector('#subjectRadios input[type="radio"]:checked');
    return checkedRadio ? checkedRadio.value : null;
}

/**
 * 显示新增科目模态框
 */
window.showAddSubjectModal = async function() {
    const gradeBtn = document.querySelector('#gradeBtnGroup .btn.active');
    const grade = gradeBtn ? gradeBtn.getAttribute('data-grade') : '';

    if (!grade) {
        Swal.fire('提示', '请先选择年级', 'info');
        return;
    }

    const result = await Swal.fire({
        title: '新增科目',
        html: `
            <form id="addSubjectForm">
                <div class="mb-3">
                    <label class="form-label">科目名称 <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" name="course_name" required placeholder="请输入科目名称">
                </div>
                <div class="mb-3">
                    <label class="form-label">课程学分 <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" name="credit" required min="0.1" max="999" step="0.1" placeholder="请输入课程学分">
                </div>
                <div class="mb-3">
                    <label class="form-label">课程满分 <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" name="full_mark" required min="1" max="9999" step="0.1" placeholder="请输入课程满分">
                </div>
                <div class="mb-3">
                    <label class="form-label">科目类型</label>
                    <select class="form-select" name="type">
                        <option value="必修">必修</option>
                        <option value="选修">选修</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">课程编码</label>
                    <input type="text" class="form-control" name="course_code" readonly placeholder="系统自动生成" style="background-color: #f8f9fa;">
                    <small class="form-text text-muted">课程编码将根据科目名称自动生成</small>
                </div>
            </form>
        `,
        showCancelButton: true,
        confirmButtonText: '添加',
        cancelButtonText: '取消',
        width: 500,
        preConfirm: () => {
            const form = document.getElementById('addSubjectForm');
            const courseName = form.course_name.value.trim();
            const credit = form.credit.value.trim();
            const fullMark = form.full_mark.value.trim();

            // 验证必填字段
            if (!courseName) {
                Swal.showValidationMessage('科目名称不能为空');
                return false;
            }
            if (!credit) {
                Swal.showValidationMessage('课程学分不能为空');
                return false;
            }
            if (!fullMark) {
                Swal.showValidationMessage('课程满分不能为空');
                return false;
            }

            // 验证数值范围
            const creditNum = parseFloat(credit);
            const fullMarkNum = parseFloat(fullMark);

            if (isNaN(creditNum) || creditNum <= 0 || creditNum > 999) {
                Swal.showValidationMessage('课程学分必须在0.1-999之间');
                return false;
            }
            if (isNaN(fullMarkNum) || fullMarkNum <= 0 || fullMarkNum > 9999) {
                Swal.showValidationMessage('课程满分必须在1-9999之间');
                return false;
            }

            return {
                course_name: courseName,
                credit: creditNum,
                full_mark: fullMarkNum,
                type: form.type.value,
                grade: grade
            };
        }
    });

    if (result.isConfirmed) {
        try {
            const response = await fetch('/api/subjects', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(result.value)
            });
            const data = await response.json();

            if (data.success) {
                Swal.fire('成功', '科目添加成功', 'success');
                // 重新加载科目列表
                loadSubjectsForTeacher(grade);
            } else {
                Swal.fire('错误', data.error || '添加失败', 'error');
            }
        } catch (error) {
            console.error('添加科目失败:', error);
            Swal.fire('错误', '网络错误，添加失败', 'error');
        }
    }
};

/**
 * 为教师加载科目数据
 */
async function loadSubjectsForTeacher(grade) {
    try {
        const response = await fetch(`/api/subjects?grade=${encodeURIComponent(grade)}`);
        const data = await response.json();

        if (data.success && data.subjects) {
            renderSubjectRadios(data.subjects);
        } else {
            console.error('获取科目数据失败:', data.error);
            // 使用默认科目
            const defaultSubjects = [
                {name: '语文', type: '必修'},
                {name: '数学', type: '必修'},
                {name: '英语', type: '必修'},
                {name: '物理', type: '必修'},
                {name: '化学', type: '必修'},
                {name: '生物', type: '必修'},
                {name: '政治', type: '必修'},
                {name: '历史', type: '必修'},
                {name: '地理', type: '必修'}
            ];
            renderSubjectRadios(defaultSubjects);
        }
    } catch (error) {
        console.error('加载科目数据失败:', error);
        // 使用默认科目
        const defaultSubjects = [
            {name: '语文', type: '必修'},
            {name: '数学', type: '必修'},
            {name: '英语', type: '必修'},
            {name: '物理', type: '必修'},
            {name: '化学', type: '必修'},
            {name: '生物', type: '必修'},
            {name: '政治', type: '必修'},
            {name: '历史', type: '必修'},
            {name: '地理', type: '必修'}
        ];
        renderSubjectRadios(defaultSubjects);
    }
}

/**
 * 渲染科目单选框
 */
function renderSubjectRadios(subjects) {
    const container = document.getElementById('subjectRadios');
    if (!container) return;

    // 按类型分组
    const groupedSubjects = {};
    subjects.forEach(subject => {
        const type = subject.type || '必修';
        if (!groupedSubjects[type]) {
            groupedSubjects[type] = [];
        }
        groupedSubjects[type].push(subject);
    });

    let html = '';

    // 渲染各个类型的科目
    Object.keys(groupedSubjects).forEach(type => {
        html += `<div class="subject-radio-group">`;
        html += `<div class="subject-type-title">${type}课程</div>`;

        groupedSubjects[type].forEach(subject => {
            html += `
                <div class="subject-radio">
                    <input type="radio" id="subject_${subject.name}" name="teacherSubject" value="${subject.name}" onchange="updateSubjectSelectorHeader()">
                    <label for="subject_${subject.name}">${subject.name}</label>
                </div>
            `;
        });

        html += `</div>`;
    });

    container.innerHTML = html;
}

/**
 * 设置选中的科目（用于编辑时）
 */
function setSelectedSubject(subjectName) {
    if (!subjectName) return;

    const radio = document.getElementById(`subject_${subjectName}`);
    if (radio) {
        radio.checked = true;
        updateSubjectSelectorHeader();
    }
}

/**
 * 自动调整textarea高度
 */
window.autoResizeTextarea = function(textarea) {
    textarea.style.height = 'auto';
    textarea.style.height = Math.max(42, textarea.scrollHeight) + 'px';
};

// ========================================
// 编辑教师界面的科目选择器函数
// ========================================

/**
 * 切换编辑教师界面科目选择器的展开/收起状态
 */
window.toggleEditSubjectSelector = function() {
    const content = document.getElementById('editSubjectSelectorContent');
    const icon = document.getElementById('editSubjectSelectorIcon');
    const selector = document.getElementById('editSubjectSelector');

    if (content.style.display === 'none') {
        content.style.display = 'block';
        icon.className = 'bi bi-chevron-up';
        selector.classList.add('expanded');
    } else {
        content.style.display = 'none';
        icon.className = 'bi bi-chevron-down';
        selector.classList.remove('expanded');
    }
};

/**
 * 更新编辑教师界面科目选择器头部显示的文本
 */
function updateEditSubjectSelectorHeader() {
    const selectedSubject = getEditSelectedSubject();
    const headerText = document.querySelector('#editSubjectSelector .subject-selector-header span');

    if (!headerText) return;

    if (!selectedSubject) {
        headerText.textContent = '请选择科目';
    } else {
        headerText.textContent = selectedSubject;
    }
}

/**
 * 获取编辑教师界面选中的科目
 */
function getEditSelectedSubject() {
    const checkedRadio = document.querySelector('#editSubjectRadios input[type="radio"]:checked');
    return checkedRadio ? checkedRadio.value : null;
}

/**
 * 为编辑教师界面加载科目数据
 */
async function loadSubjectsForEditTeacher(grade, currentSubject) {
    try {
        const response = await fetch(`/api/subjects?grade=${encodeURIComponent(grade)}`);
        const data = await response.json();

        if (data.success && data.subjects) {
            renderEditTeacherSubjectRadios(data.subjects, currentSubject);
        } else {
            console.error('获取科目数据失败:', data.error);
            // 使用默认科目
            const defaultSubjects = [
                {name: '语文', type: '必修'},
                {name: '数学', type: '必修'},
                {name: '英语', type: '必修'},
                {name: '物理', type: '必修'},
                {name: '化学', type: '必修'},
                {name: '生物', type: '必修'},
                {name: '政治', type: '必修'},
                {name: '历史', type: '必修'},
                {name: '地理', type: '必修'}
            ];
            renderEditTeacherSubjectRadios(defaultSubjects, currentSubject);
        }
    } catch (error) {
        console.error('加载科目数据失败:', error);
        // 使用默认科目
        const defaultSubjects = [
            {name: '语文', type: '必修'},
            {name: '数学', type: '必修'},
            {name: '英语', type: '必修'},
            {name: '物理', type: '必修'},
            {name: '化学', type: '必修'},
            {name: '生物', type: '必修'},
            {name: '政治', type: '必修'},
            {name: '历史', type: '必修'},
            {name: '地理', type: '必修'}
        ];
        renderEditTeacherSubjectRadios(defaultSubjects, currentSubject);
    }
}

/**
 * 渲染编辑教师界面的科目单选框
 */
function renderEditTeacherSubjectRadios(subjects, currentSubject) {
    const container = document.getElementById('editTeacherSubjectRadios');
    if (!container) return;

    // 解析当前选中的科目（取第一个，因为现在是单选）
    const selectedSubject = currentSubject ? currentSubject.split(',')[0].trim() : '';

    // 按类型分组
    const groupedSubjects = {};
    subjects.forEach(subject => {
        const type = subject.type || '必修';
        if (!groupedSubjects[type]) {
            groupedSubjects[type] = [];
        }
        groupedSubjects[type].push(subject);
    });

    let html = '';

    // 渲染各个类型的科目
    Object.keys(groupedSubjects).forEach(type => {
        html += `<div class="subject-radio-group">`;
        html += `<div class="subject-type-title">${type}课程</div>`;

        groupedSubjects[type].forEach(subject => {
            const isChecked = selectedSubject === subject.name;
            html += `
                <div class="subject-radio">
                    <input type="radio" id="edit_teacher_subject_${subject.name}" name="editTeacherSubject" value="${subject.name}"
                           ${isChecked ? 'checked' : ''} onchange="updateEditTeacherSubjectSelectorHeader()">
                    <label for="edit_teacher_subject_${subject.name}">${subject.name}</label>
                </div>
            `;
        });

        html += `</div>`;
    });

    container.innerHTML = html;

    // 更新头部显示
    updateEditTeacherSubjectSelectorHeader();
}

// ========================================
// 新增教师界面的科目选择器函数
// ========================================

/**
 * 切换新增教师界面科目选择器的展开/收起状态
 */
window.toggleAddTeacherSubjectSelector = function() {
    const content = document.getElementById('addTeacherSubjectSelectorContent');
    const icon = document.getElementById('addTeacherSubjectSelectorIcon');
    const selector = document.getElementById('addTeacherSubjectSelector');

    if (content.style.display === 'none') {
        content.style.display = 'block';
        icon.className = 'bi bi-chevron-up';
        selector.classList.add('expanded');
    } else {
        content.style.display = 'none';
        icon.className = 'bi bi-chevron-down';
        selector.classList.remove('expanded');
    }
};

/**
 * 更新新增教师界面科目选择器头部显示的文本
 */
function updateAddTeacherSubjectSelectorHeader() {
    const selectedSubject = getAddTeacherSelectedSubject();
    const headerText = document.querySelector('#addTeacherSubjectSelector .subject-selector-header span');

    if (!headerText) return;

    if (!selectedSubject) {
        headerText.textContent = '请选择科目';
    } else {
        headerText.textContent = selectedSubject;
    }
}

/**
 * 获取新增教师界面选中的科目
 */
function getAddTeacherSelectedSubject() {
    const checkedRadio = document.querySelector('#addTeacherSubjectRadios input[type="radio"]:checked');
    return checkedRadio ? checkedRadio.value : null;
}

/**
 * 为新增教师界面加载科目数据
 */
async function loadSubjectsForAddTeacher(grade) {
    try {
        const response = await fetch(`/api/subjects?grade=${encodeURIComponent(grade)}`);
        const data = await response.json();

        if (data.success && data.subjects) {
            renderAddTeacherSubjectRadios(data.subjects);
        } else {
            console.error('获取科目数据失败:', data.error);
            // 使用默认科目
            const defaultSubjects = [
                {name: '语文', type: '必修'},
                {name: '数学', type: '必修'},
                {name: '英语', type: '必修'},
                {name: '物理', type: '必修'},
                {name: '化学', type: '必修'},
                {name: '生物', type: '必修'},
                {name: '政治', type: '必修'},
                {name: '历史', type: '必修'},
                {name: '地理', type: '必修'}
            ];
            renderAddTeacherSubjectRadios(defaultSubjects);
        }
    } catch (error) {
        console.error('加载科目数据失败:', error);
        // 使用默认科目
        const defaultSubjects = [
            {name: '语文', type: '必修'},
            {name: '数学', type: '必修'},
            {name: '英语', type: '必修'},
            {name: '物理', type: '必修'},
            {name: '化学', type: '必修'},
            {name: '生物', type: '必修'},
            {name: '政治', type: '必修'},
            {name: '历史', type: '必修'},
            {name: '地理', type: '必修'}
        ];
        renderAddTeacherSubjectRadios(defaultSubjects);
    }
}

/**
 * 渲染新增教师界面的科目单选框
 */
function renderAddTeacherSubjectRadios(subjects) {
    const container = document.getElementById('addTeacherSubjectRadios');
    if (!container) return;

    // 按类型分组
    const groupedSubjects = {};
    subjects.forEach(subject => {
        const type = subject.type || '必修';
        if (!groupedSubjects[type]) {
            groupedSubjects[type] = [];
        }
        groupedSubjects[type].push(subject);
    });

    let html = '';

    // 渲染各个类型的科目
    Object.keys(groupedSubjects).forEach(type => {
        html += `<div class="subject-radio-group">`;
        html += `<div class="subject-type-title">${type}课程</div>`;

        groupedSubjects[type].forEach(subject => {
            html += `
                <div class="subject-radio">
                    <input type="radio" id="add_teacher_subject_${subject.name}" name="addTeacherSubject" value="${subject.name}"
                           onchange="updateAddTeacherSubjectSelectorHeader()">
                    <label for="add_teacher_subject_${subject.name}">${subject.name}</label>
                </div>
            `;
        });

        html += `</div>`;
    });

    container.innerHTML = html;
}

/**
 * 切换编辑教师界面科目选择器的展开/收起状态
 */
window.toggleEditTeacherSubjectSelector = function() {
    const content = document.getElementById('editTeacherSubjectSelectorContent');
    const icon = document.getElementById('editTeacherSubjectSelectorIcon');
    const selector = document.getElementById('editTeacherSubjectSelector');

    if (content.style.display === 'none') {
        content.style.display = 'block';
        icon.className = 'bi bi-chevron-up';
        selector.classList.add('expanded');
    } else {
        content.style.display = 'none';
        icon.className = 'bi bi-chevron-down';
        selector.classList.remove('expanded');
    }
};

/**
 * 更新编辑教师界面科目选择器头部显示的文本
 */
function updateEditTeacherSubjectSelectorHeader() {
    const selectedSubject = getEditTeacherSelectedSubject();
    const headerText = document.querySelector('#editTeacherSubjectSelector .subject-selector-header span');

    if (!headerText) return;

    if (!selectedSubject) {
        headerText.textContent = '请选择科目';
    } else {
        headerText.textContent = selectedSubject;
    }
}

/**
 * 获取编辑教师界面选中的科目
 */
function getEditTeacherSelectedSubject() {
    const checkedRadio = document.querySelector('#editTeacherSubjectRadios input[type="radio"]:checked');
    return checkedRadio ? checkedRadio.value : null;
}
